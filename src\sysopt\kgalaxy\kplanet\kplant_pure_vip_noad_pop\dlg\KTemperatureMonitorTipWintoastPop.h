 #pragma once
#include "KPopBase.h"
#include "CBkMenu.h"
#include "cf_public/core/theme/KSystemThemeChangeMonitor.h"
#include "cf_public/component/win_toast/win_toast_interface.h"

#include "duba_include\easyipc_new\easyipcclientwrap.h"
#include "duba_include\easyipc_new\easyipceventwrap.h"

class KTemperatureMonitorTipWintoastPop : public KPopBase
{
public:
    KTemperatureMonitorTipWintoastPop(kplanet::KPopTask* pHost);
    ~KTemperatureMonitorTipWintoastPop(void);

public: // public IShadowPaintHook, public KPopBase, public BKMenuCallBack
    virtual BOOL Init(CString strJsonParam);
    virtual BOOL UnInit();
    virtual DWORD GetPopId() {return ktemperature_monitor_tip_wintoast_pop_id;}
    virtual CString GetPopName() {return CF_STR_KTEMPERATURE_MONITOR_TIP_WINTOAST_POP_NAME;}
    virtual BOOL CanShow();
    virtual BOOL Show();

private:
    bool IsHighTemperaturePopType();

    bool IsCanShowByMagicCube();

protected:
    kplanet::KPopTask* m_pHost;
    ULONGLONG m_ullTemperature;
    UINT m_ulType;
    KEasyIpcClientWrap m_ipcClient; 
};
