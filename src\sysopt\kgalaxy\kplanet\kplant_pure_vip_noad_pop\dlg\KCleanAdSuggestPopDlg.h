#pragma once
#include "KPopBase.h"
#include "CBkMenu.h"
#include "../KReporter.h"

class KCleanAdSuggestPopDlg : public CBkDialogViewEx, public IShadowPaintHook, public KPopBase, public BKMenuCallBack
{
public:
    KCleanAdSuggestPopDlg(kplanet::KPopTask* pHost);
    ~KCleanAdSuggestPopDlg(void);

#define UM_MSG_AFTERINIT   (WM_USER + 1)

    enum enumResID{
        ID_CLOSE = 100,
        ID_MAIN_BUTTON = 101,
        ID_SETTING = 102,
        ID_MORETHANONESOFT_TEXT = 103,
        ID_ONLYONESOFT_TEXT = 104
    };

    enum enumStringID{
        ID_STRING_CONTENT = 100
    };

    enum enumSettingMenuId{
        ID_NOMORENOTIFY = 1,
        ID_MENU_CLOSE,
    };

public:
    BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
        BK_NOTIFY_ID_COMMAND(ID_CLOSE, OnBtnClose)
        BK_NOTIFY_ID_COMMAND(ID_SETTING, OnBtnSetting)
        BK_NOTIFY_ID_COMMAND(ID_MAIN_BUTTON, OnBtnClick)
        BK_NOTIFY_MAP_END()

        BEGIN_MSG_MAP_EX(KCleanAdSuggestPopDlg)
        MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
        MSG_WM_INITDIALOG(OnInitDialog)
        MESSAGE_HANDLER(UM_MSG_AFTERINIT, OnMessageAfterInit)
        MSG_WM_TIMER(OnTimer)
		CHAIN_MSG_MAP(CBkDialogViewEx)
    END_MSG_MAP()

public:
    virtual BOOL Init(CString strJsonParam);
    virtual BOOL UnInit();
    virtual DWORD GetPopId(){return clean_ad_pop_id;}
    virtual CString GetPopName(){return clean_ad_pop_name;}
    virtual BOOL CanShow();
    virtual BOOL Show();

    virtual void MenuItemClick(int menuId);

    virtual BOOL NotifyShadowPaint(HDC hDC, CRect& rct);
    LRESULT OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/);
	void OnTimer(UINT_PTR nIDEvent);
    void OnBtnClose();
    void OnBtnSetting();
    void OnBtnClick();
    LRESULT OnMessageAfterInit(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/);

protected:
    BOOL _IsAvoidByNDayShowCondition();
    BOOL _IsSelectNoMoreNotify();
    BOOL _SetLastShowTime();
    BOOL _SetNoMoreNotifyFlag();

    BOOL _IsAvoidByNDayScanCondition();
    BOOL _SetLastScanTime();

    BOOL _InitShowFrom(CString strJsonParam);
    BOOL _InitVipInfo();
    DWORD _GetNeedPurifySoftCount();
    void _OpenSoftpuriferAndScan();
    void _OpenSoftPuriferAndScanByBackGround();

protected:
    kplanet::KPopTask* m_pHost;
    CleanAdSuggestPopReportInfo m_behaveInfo;
    CBKMenu m_settingMenu;
    DWORD m_dwNeedPurifySoftCount;
};
