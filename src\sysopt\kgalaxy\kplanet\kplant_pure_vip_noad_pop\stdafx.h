// stdafx.h : include file for standard system include files,
// or project specific include files that are used frequently, but
// are changed infrequently
//

#pragma once

// Modify the following defines if you have to target a platform prior to the ones specified below.
// Refer to MSDN for the latest info on corresponding values for different platforms.
#ifndef WINVER				// Allow use of features specific to Windows XP or later.
#define WINVER 0x0601		// Change this to the appropriate value to target other versions of Windows.
#endif

#ifndef _WIN32_WINNT		// Allow use of features specific to Windows XP or later.                   
#define _WIN32_WINNT 0x0601	// Change this to the appropriate value to target other versions of Windows.
#endif						

#ifndef _WIN32_WINDOWS		// Allow use of features specific to Windows 98 or later.
#define _WIN32_WINDOWS 0x0410 // Change this to the appropriate value to target Windows Me or later.
#endif

#ifndef _WIN32_IE			// Allow use of features specific to IE 6.0 or later.
#define _WIN32_IE 0x0700	// Change this to the appropriate value to target other versions of IE.
#endif

#define WIN32_LEAN_AND_MEAN		// Exclude rarely-used stuff from Windows headers

#include "include/buildcfg/buildcfg.h"

// Windows Header Files:
#include <windows.h>
#include <atlbase.h>
#include <atlstr.h>
#include <atlwin.h>
#include <atltrace.h>
#include <gdiplus.h>
#include <shellapi.h>

#define _WTL_NO_CSTRING
#include "cf_3rdparty/wtl_10/atlapp.h"
#include "cf_3rdparty/wtl_10//atlcrack.h"
#include "cf_3rdparty/wtl_10//atlsplit.h"
#include "cf_3rdparty/wtl_10//atlframe.h"
#include "cf_3rdparty/wtl_10//atlgdi.h"
#include "cf_3rdparty/wtl_10//atlctrls.h"
#include "cf_3rdparty/wtl_10//atlctrlx.h"
#include "cf_3rdparty/wtl_10//atlmisc.h"

// TODO: reference additional headers your program requires here
#include "cf_public/3rd/xml/tinyxml.hpp"
#include "kis/src/GUI/publish/bkwin/bklistview.h"
#include "kis/src/GUI/publish/bkwin/cbkdialogviewimplex.h"
#include "publish/kskinmgr/kisskinmanager.h"
#include "kis/src/GUI/publish/bkres/bkres.h"
#include "cf_public/core/kis_com_s/kis_com/kiscom.h"
#include "include/kfixstar/kpoploader.h"
#include "cf_public/core/json/bkjson.h"
#include "include/kfixstar/kpoputils.h"
#include "cf_public/core/path/path.hpp"
#include "include/infoc/KInfocWrap.h"
#include "cf_public/component/product/KCheckVersion.h"
#define KInfocClientWrapDef(xx)	KInfocClientWrapEx xx(KCheckVersionInstance.GetCurrentVersion())