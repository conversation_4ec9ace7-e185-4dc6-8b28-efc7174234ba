#include "StdAfx.h"
#include "KPlanetMainDlg.h"
#include "../KReporter.h"
#include "publish/purevip/knewvip_wrapper.hpp"
#include "publish/purevip/pure_vip_switch.h"

KPlanetMainDlg::KPlanetMainDlg(kplanet::KPopTask* pHost) : CBkDialogViewEx(IDR_PUREVIP_NOAD_DLG), m_pHost(pHost), is_expired(false)
{
    EnableShadow(TRUE);
    SetDrawBorder(FALSE);
    SetShadowSize(28);
    SetShadowPaintHook(this);
}

KPlanetMainDlg::~KPlanetMainDlg(void)
{
}

BOOL KPlanetMainDlg::NotifyShadowPaint(HDC hDC, CRect& rct)
{
    WINDOWPOS wndPos = {0};
    CBkExpendImg exmPandImg;

    wndPos.x = rct.left;
    wndPos.y = rct.top;
    wndPos.cx = rct.Width();
    wndPos.cy = rct.Height();

    exmPandImg.SetAttribute("pos", "0,0,-0,-0", TRUE);
    exmPandImg.BkSendMessage(WM_WINDOWPOSCHANGED, NULL, (LPARAM)&wndPos);
    exmPandImg.SetAttribute("margin_left", "28", TRUE);
    exmPandImg.SetAttribute("margin_right", "28", TRUE);
    exmPandImg.SetAttribute("margin_top", "28", TRUE);
    exmPandImg.SetAttribute("margin_bottom", "28", TRUE);
    exmPandImg.SetAttribute("skin", "shadow", TRUE);
    exmPandImg.BkSendMessage(WM_PAINT, (WPARAM)hDC);
    return FALSE;
}

LRESULT KPlanetMainDlg::OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/)
{
    BkJson::Document jsonParam;
	ModifyStyleEx(0, WS_EX_TOOLWINDOW);
    return TRUE;
}

void KPlanetMainDlg::OnBtnClose()
{
    BkJson::Document jsonQuery;
    jsonQuery[L"result"] = L"click close";
    m_pHost->SetJsonResult(jsonQuery.Dump());
    m_pHost->SetHideReason(KPopCenterDef::PopHideReasonCode_ClickClose);
    KReporter::ReportPopResult(1, click_close, avoid_unknown);
    EndDialog(-1);
}

void KPlanetMainDlg::OnBtnClick()
{
    if (pure_vip::SetNewVipSwitchOn())
	    OpenVipCenter();
	 m_pHost->SetHideReason(KPopCenterDef::PopHideReasonCode_ClickBtn);
     KReporter::ReportPopResult(1, click_view, avoid_unknown);
	 EndDialog(-1);
}

void KPlanetMainDlg::OpenVipCenter()
{
    CString sPath = KDubaPath::GetModuleFolder(NULL);
    ::PathAppend(sPath.GetBuffer(MAX_PATH), _T("knewvip.exe"));
    sPath.ReleaseBuffer();
    CString sCmd;
    sCmd.Format(L"-open_opction=%d -from=%d", purevip::eOpenSpreadAd, purevip::eVipFrom_Close_AD_Pop);
    ShellExecute(NULL, L"open", sPath, sCmd, NULL, SW_SHOWNORMAL);
}