﻿#include "StdAfx.h"
#include "KCommonPopDlg.h"
#include "../KReporter.h"

#include "duba_include\purevip\pure_vip_switch.h"
#include "duba_include/purevip/knewvip_wrapper.hpp"
#include "publish/systemopt/KPopConfig.h"

#include "include\framework\KRegister2.h"
#include "publish\kvip\getvipinfowarpper.h"
#include "include\framework\KFileVersion.h"
#include "cf_public/core/time/KTime.h"
#include "duba_include\purevip\pure_vip_wrapper.hpp"
#include "duba_include\purevip\KVipUsageRecord.h"
#include "cf_public/component/product/KCheckVersion.h"
#include "publish\kpopconfig\KPopCommFunc.hpp"
#include "cf_public\core\string\sys_string_conversions.h"
#include "cf_public/core/disk/DiskUtil.h"
#include "publish\systemopt\KFunction.h"
#include "include\kschrodingercat\IKABTester.h"
#include "publish\kpopconfig\KSoftUninstallPopCommonFunc.hpp"
#include "include/framework/ScreenUtil.h"
#include "publish/systemopt/pop_name_def.h"
#include "cf_public\component\magiccube\include\KSdkMagicCubeWarpper.h"
#include "cf_public\core\log\logging.h"

#define DRIVER_MANAGER_SHOW_MAX_ITEM 2
#define CF_STR_SYS_DISK_SLIM_PROCEDURE_NAME	L"ksysslim.exe"

KCommonPopDlg::KCommonPopDlg(kplanet::KPopTask* pHost, basePop::PopType enumPopType) : 
	CBkDialogViewEx(GetXmlResId(enumPopType))
	, m_pHost(pHost)
	, m_enumPopType(enumPopType)
	, m_nType(0)
	, m_nSrc(1)
	, m_nPopTime(POP_TIME::PT_PRESCAN)
    , m_systemThemeStyle(cf::theme::DARK)
    , m_nNum(0)
{
    EnableShadow(TRUE);
    SetDrawBorder(FALSE);
    SetShadowSize(28);
    SetShadowPaintHook(this);
    m_ullSize = 0;
    m_productType = KCheckVersionInstance.GetCurrentVersion();
}

KCommonPopDlg::~KCommonPopDlg(void)
{
}

BOOL KCommonPopDlg::Init(CString strJsonParam)
{
    cf::BkJson::Document doc;
	if (!doc.LoadString(strJsonParam))
		return FALSE;

    switch (m_enumPopType)
    {
    case basePop::POPTYPE_DRIVER_MANAGER:
        {
            BOOL bRet = KDriverManagerCommFunc::LoadJsonFromString(strJsonParam, m_vecDeviceInfo);
            m_ullSize = m_vecDeviceInfo.size();
            CString strPath = KCheckVersionInstance.GetCurrentInstalledPath();
            KDubaPath::PathAddBackslash(strPath);
            strPath.Append(L"data/drivermanagercfg.dat");
            KDriverManagerCommFunc::SaveInfoToPath(strPath, m_vecDeviceInfo);
            return bRet;
        }
        break;
    case basePop::POPTYPE_KLSOFTMGR:
    case basePop::POPTYPE_FILEDESTROY:
        {
            if (!doc.HasKey(L"size"))
                return FALSE;

            CString strSize = doc[L"size"];
            _stscanf(strSize.GetString(), _T("%I64d"), &m_ullSize);
        }
        break;
	case basePop::POPTYPE_SYS_SLIM_CLEAN_OLD:
		{			
			if (!ProcessPopTime(doc))
			{
				return FALSE;
			}
			
			m_nSrc = POP_STYLE::PS_OLD;
		}
		break;
	case basePop::POPTYPE_SYS_SLIM_CLEAN_NEW:
		{
			if (!ProcessPopTime(doc))
			{
				return FALSE;
			}

			m_nSrc = POP_STYLE::PS_NEW;
		}
		break;
    case basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY:
        if (doc.HasKey(L"size"))
        {
            CString strSize = doc[L"size"];
            _stscanf(strSize.GetString(), _T("%I64d"), &m_ullSize);
        }
        if (doc.HasKey(L"softname"))
        {
            m_strSoftName = doc[L"softname"];
        }
        if (doc.HasKey(L"num"))
        {
            m_nNum = doc[L"num"];
        }
        break;
    default:
        m_strParamJson = strJsonParam;
        break;
    }

    return TRUE;
}

BOOL KCommonPopDlg::UnInit()
{
    return TRUE;
}


DWORD KCommonPopDlg::GetPopId()
{
    int nSize = _countof(basePop::g_popInfo);
    if (m_enumPopType >= nSize || m_enumPopType < 0)
        return 0;
    return basePop::g_popInfo[m_enumPopType].nPopId;
}


CString KCommonPopDlg::GetPopName()
{
    int nSize = _countof(basePop::g_popInfo);
    if (m_enumPopType >= nSize || m_enumPopType < 0)
        return L"";
    return basePop::g_popInfo[m_enumPopType].strPopName;
}

BOOL KCommonPopDlg::CanShow()
{
    BOOL bRet = true;
    
    switch(m_enumPopType)
    {
    case basePop::POPTYPE_FILEDESTROY:
        {
            int nStragegyId = GetStrategyId(L"{FB1C0B4A-C3F1-4f76-A5C7-5727252D2355}");
            KReporter::ReportFileDestroyPop(0, m_ullSize, nStragegyId == ABTEST_HIT ? ABTEST_HIT : ABTEST_NOT_HIT);
            bRet = ABTEST_HIT == nStragegyId;
        }
        break;
    case basePop::PopType::POPTYPE_KLSOFTMGR_ANTIREINSTALL_RET:
        {
            //> 点击不再提醒不弹
            //> 点击次软件不再提醒不弹
            bRet = CanAntiReinstallRetPopShow();
            break;
        }
	case basePop::PopType::POPTYPE_KLSOFTMGR_ANTIREINSTALL:
		{
			bRet = CanAntiReinstallPopShow();
			break;
		}
		break;
    case basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY:
        if(KPopCommFunc::GetInstance()->GetPopNoShowAnymore(GetPopName()))
        {
            bRet=FALSE;
        }
        break;
    default:
        break;
    }
    return bRet;
}

BOOL KCommonPopDlg::Show()
{
    DWORD now = time(NULL);
    switch (m_enumPopType)
    {
    case basePop::POPTYPE_DRIVER_MANAGER:
    case basePop::POPTYPE_FILEDESTROY:
    case basePop::POPTYPE_KLSOFTMGR:
    case basePop::POPTYPE_KLSOFTMGR_ANTIREINSTALL:
    case basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY:
        KPopCommFunc::GetInstance()->SetPopLastShowTime(GetPopName(), now);
        break;
	case basePop::POPTYPE_SYS_SLIM_CLEAN_OLD:
		{
			KPopCfgInstance::Instance().SetPopCurrentShowTime( SYSSLIM_POP_NAME );
		}
		break;
	case basePop::POPTYPE_SYS_SLIM_CLEAN_NEW:
		{
			KPopCfgInstance::Instance().SetPopCurrentShowTime( SYSSLIM_POP_NAME_2 );
		}
		break;
    default:
        break;
    }
	DoModal(NULL, CRect(0,0,0,0));
    return TRUE;
}

BOOL KCommonPopDlg::NotifyShadowPaint(HDC hDC, CRect& rct)
{
	if (basePop::POPTYPE_SYS_SLIM_CLEAN_OLD == m_enumPopType || (basePop::POPTYPE_SYS_SLIM_CLEAN_NEW == m_enumPopType && m_bOldStyle))
	{
		return FALSE;
	}

    WINDOWPOS wndPos = {0};
    CBkExpendImg exmPandImg;

    wndPos.x = rct.left;
    wndPos.y = rct.top;
    wndPos.cx = rct.Width();
    wndPos.cy = rct.Height();

    exmPandImg.SetAttribute("pos", "0,0,-0,-0", TRUE);
    exmPandImg.BkSendMessage(WM_WINDOWPOSCHANGED, NULL, (LPARAM)&wndPos);
    exmPandImg.SetAttribute("margin_left", "28", TRUE);
    exmPandImg.SetAttribute("margin_right", "28", TRUE);
    exmPandImg.SetAttribute("margin_top", "28", TRUE);
    exmPandImg.SetAttribute("margin_bottom", "28", TRUE);
    exmPandImg.SetAttribute("skin", "shadow", TRUE);
    exmPandImg.BkSendMessage(WM_PAINT, (WPARAM)hDC);
    return FALSE;
}

LRESULT KCommonPopDlg::OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/)
{
	cf::BkJson::Document jsonParam;
    ModifyStyleEx(0, WS_EX_TOOLWINDOW);

    CString strText;
    switch (m_enumPopType)
    {
    case basePop::POPTYPE_DRIVER_MANAGER:
        {
            InitDriverManagerUi(strText);
        }
        break;
    case basePop::POPTYPE_FILEDESTROY:
        strText.Format(BkString::Get(110), GetDlgTextByMemorySize(m_ullSize));
        break;
    case basePop::POPTYPE_KLSOFTMGR:
        {
            InitKLSoftMgrUiCommon();
            GetKLSoftMgrPopTxt(strText);
        }
        break;
	case basePop::POPTYPE_SYS_SLIM_CLEAN_OLD:
		{
			InitSysSLimPopCommon();
			InitSysSLimPopOld();
		}
		break;
	case basePop::POPTYPE_SYS_SLIM_CLEAN_NEW:
		{
			InitSysSLimPopCommon();
			InitSysSLimPopNew();
		}
		break;
    case basePop::POPTYPE_KLSOFTMGR_ANTIREINSTALL:
    case basePop::PopType::POPTYPE_KLSOFTMGR_ANTIREINSTALL_RET:
        {
            InitKLSoftMgrUiCommon();

            DWORD dwSoftId;
            CString strSoftName;
            GetParam_SoftMgrAntiReInstall(strSoftName, dwSoftId);

            if (!strSoftName.IsEmpty())
            {
                strText.Format(BkString::Get(129), strSoftName);
            }
			m_strSoftName = strSoftName;
        }
        break;
    case basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY:
        {
            CString strText;
            strText.Format( BkString::Get(154) , m_ullSize);
            SetRichText(ID_MAIN_TEXT, strText);

            if (KCheckVersionInstance.GetCurrentVersion()
                == cf::product::enum_ProductType_DriverGenius){
                SetItemAttribute(ID_LOGO, "skin", "logo_dg_white");
                SetItemText(ID_TEXT_TITLE, L"驱动精灵");
            }
        }
        break;
    default:
        break;
    }
    SetRichText(ID_TIP_TEXT, strText);

    if (!m_bOldStyle)
    {
        SwitchToStyle(m_systemThemeStyle);
    }

    PostMessage(UM_MSG_AFTERINIT);
    SetTimer(auto_close_timer, max_show_time);

    if (m_enumPopType != basePop::POPTYPE_FILEDESTROY && m_enumPopType != basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY)
    {
        ReportPop(ID_DEFAULT_OPEN);
    }

    return TRUE;
}

void KCommonPopDlg::OnBtnClose()
{
    int nCloseAction = 2;
    if (m_enumPopType == basePop::POPTYPE_DRIVER_MANAGER)
    {
        nCloseAction = ID_CLICK_CLOSE;
    }
    else if (m_enumPopType == basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY)
    {
        nCloseAction = 0;
    }
    ReportPop(nCloseAction);
	cf::BkJson::Document jsonQuery;
    jsonQuery[L"result"] = L"click close";
    m_pHost->SetJsonResult(jsonQuery.Dump());
    m_pHost->SetHideReason(KPopCenterDef::PopHideReasonCode_ClickClose);

    //解决某些电脑未将上报数据加入队列中导致上报未成功问题
    Sleep(100);
	EndDialog(-1);
}

void KCommonPopDlg::MenuItemClick( int menuId )
{
    int nCleanAction = 0;
	switch (menuId)
	{
    case MENU_ID::MI_NOT_REMIND_AGAIN:
		{
            switch (m_enumPopType)
            {
            case basePop::POPTYPE_DRIVER_MANAGER:
            case basePop::POPTYPE_FILEDESTROY:
            case basePop::POPTYPE_KLSOFTMGR_ANTIREINSTALL_RET:
            case basePop::POPTYPE_KLSOFTMGR:
                KSoftUninstallPopCfgMgr.SetTipPopNoShowTime(_time64(NULL), klsoftmgr::EmPopNormal);
                ReportPop(ID_CLICK_NEVER);
                break;
			case basePop::POPTYPE_SYS_SLIM_CLEAN_OLD:
				{
					KPopCfgInstance::Instance().SetPopLocalNoShow(SYSSLIM_POP_NAME, TRUE);
					ReportPop(ID_CLICK_NEVER);
				}
				break;
			case basePop::POPTYPE_SYS_SLIM_CLEAN_NEW:
				{
					KPopCfgInstance::Instance().SetPopLocalNoShow(SYSSLIM_POP_NAME_2, TRUE);
					ReportPop(ID_CLICK_NEVER);
				}
				break;
			case basePop::POPTYPE_KLSOFTMGR_ANTIREINSTALL:
				{
					OnBtnCancelAntiReinstallPopImpl();
					ReportPop(ID_CLICK_NEVER);
				}
				break;
            default:
                break;
            }

            m_pHost->SetHideReason(KPopCenterDef::PopHideReasonCode_CloseForever);
            //解决某些电脑未将上报数据加入队列中导致上报未成功问题
            Sleep(100);
		    EndDialog(-1);
        }
        break;
	default:
		break;
	}
}

void KCommonPopDlg::OnBtnSetting()
{
    const int leftOffset = -70;
	CBkWindow* pWnd = GetBkItem(ID_SETTING_BUTTON);
	if (!pWnd)
		return;

	CRect rcRect;
	pWnd->GetRect(rcRect);
	ClientToScreen(rcRect);
    int topOffset = rcRect.Height();
	m_settingMenu.PopUp(CPoint(rcRect.left + leftOffset,rcRect.top + topOffset));
    switch (m_enumPopType)
    {
    case basePop::POPTYPE_DRIVER_MANAGER:
        ReportPop(ID_CLICK_SETTING);
        break;
    default:
        break;
    }
}

void KCommonPopDlg::OnBtnNeverShow()
{
    m_enumPopType = basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY;
    int nCleanAction = 0;
    KPopCommFunc::GetInstance()->SetPopNoShowAnymore(GetPopName());
    if (m_enumPopType == basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY)
    {
        nCleanAction = 2;  // 点击提醒上报不变
    }
    ReportPop(nCleanAction);

    m_pHost->SetHideReason(KPopCenterDef::PopHideReasonCode_CloseForever);
    //解决某些电脑未将上报数据加入队列中导致上报未成功问题
    Sleep(100);
    EndDialog(-1);

}


void KCommonPopDlg::OnBtnShowMore()
{
    OnCommonBtnOk();
}

void KCommonPopDlg::OnCommonBtnOk()
{
    int nCleanAction = 1;
    if (m_enumPopType == basePop::POPTYPE_DRIVER_MANAGER)
    {
        nCleanAction = ID_CLICK_CLEAN;
    }
    ReportPop(nCleanAction);
    CPath strPath;
	CString strParam;
    switch (m_enumPopType)
    {
   case basePop::POPTYPE_DRIVER_MANAGER:
       switch (m_productType)
       {
       case cf::product::enum_ProductType_Duba:
           {
               strPath.m_strPath = KDubaPath::GetDubaInstallPathFromReg();
               strPath.Append(L"kxemain.exe");
               strParam = L"/vip:driver_manager /vipfrom:1444 /from:5 --autochk";
           }
           break;
       case cf::product::enum_ProductType_DriverGenius:
           {
               strPath.m_strPath = KDubaPath::GetDGInstallPathFromReg();
               strPath.Append(L"drivergenius.exe");
               strParam = L"/vipfrom:3150 -enterdriverpage";
           }
           break;
       default:
           break;
       }
        break;
   case basePop::POPTYPE_FILEDESTROY:
       strPath.m_strPath = KDubaPath::GetDubaInstallPathFromReg();
       strPath.Append(L"kxemain.exe");
       strParam = L"/vip:file_remove /vipfrom:1436 /from:20";
       break;
   case basePop::POPTYPE_KLSOFTMGR:
        KLSGetExecuteCommandAndParam(strPath, strParam);
       break;
   case basePop::POPTYPE_KLSOFTMGR_ANTIREINSTALL:
       KLSAntiReinstallGetExecuteCommandAndParam(strPath, strParam);
       break;
   case basePop::POPTYPE_SYS_SLIM_CLEAN_OLD:
	   {
           strPath.m_strPath = KCheckVersionInstance.GetCurrentInstalledPath();
		   strPath.Append(CF_STR_SYS_DISK_SLIM_PROCEDURE_NAME);
		   strParam = _T("-from:10 -vipfrom:1199");
	   }
	   break;
   case basePop::POPTYPE_SYS_SLIM_CLEAN_NEW:
	   {
           strPath.m_strPath = KCheckVersionInstance.GetCurrentInstalledPath();
		   strPath.Append(CF_STR_SYS_DISK_SLIM_PROCEDURE_NAME);
		   strParam = _T("-from:103 -vipfrom:1198");
	   }
	   break;
   case basePop::PopType::POPTYPE_KLSOFTMGR_ANTIREINSTALL_RET:
       KLSAntiReinstallShowSettingGetExecuteCommandAndParam(strPath, strParam);
       break;
   case basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY:
       strPath.m_strPath = KCheckVersionInstance.GetCurrentInstalledPath();
       strPath.Append(L"kdinfomgr.exe");
       strParam = L"-slnid=5121 -opentype=1 -vip_from=2396";
       break;
   default:
        break;
    }

    ::ShellExecute(NULL, L"open", strPath, strParam, NULL, SW_SHOWNORMAL);

    m_pHost->SetHideReason(KPopCenterDef::PopHideReasonCode_ClickBtn);

    //解决某些电脑未将上报数据加入队列中导致上报未成功问题
    Sleep(100);
    EndDialog(-1);
}


void KCommonPopDlg::OnCommonBtnCancel()
{
    switch (m_enumPopType)
    {
    case basePop::PopType::POPTYPE_KLSOFTMGR_ANTIREINSTALL_RET:
        OnBtnCancelAntiReinstallRetPopImpl();
        break;
    default:
        break;
    }

    Sleep(100);
    EndDialog(-1);
}

void KCommonPopDlg::OnCommonLinkHeaderSetting()
{
    KPopCommFunc::GetInstance()->SetPopNoShowAnymore(GetPopName());

    switch (m_enumPopType)
    {
    case basePop::PopType::POPTYPE_KLSOFTMGR_ANTIREINSTALL_RET:
        {
            static int nClickNeverNotTip = 4;
            ReportPop(nClickNeverNotTip);
        }
        break;
    default:
        break;
    }

    Sleep(100);
    EndDialog(-1);
}

void KCommonPopDlg::OnBtnGiveUp()
{
	ReportPop(SLIM_CLEAN_CLICK_TYPE::SCCT_ABANDON);
	Sleep(100);
	EndDialog(-1);
}

void KCommonPopDlg::OnTimer( UINT_PTR nIDEvent )
{
	if (nIDEvent == auto_close_timer)
	{
		LASTINPUTINFO lpi;
		lpi.cbSize = sizeof(lpi);
		GetLastInputInfo(&lpi);
		DWORD dwTime = ::GetTickCount() - lpi.dwTime;
		if (dwTime > (175*1000))//有一定的误差，不能判断3分钟，用175秒吧
		{
			return;
		}

		KillTimer(auto_close_timer);
		cf::BkJson::Document jsonQuery;
		jsonQuery[L"result"] = L"3 min auto close";
		m_pHost->SetJsonResult(jsonQuery.Dump());
		m_pHost->SetHideReason(KPopCenterDef::PopHideReasonCode_OutTime);

        int nAction = ID_CLICK_AUTO_CLOSE;
        if (m_enumPopType == basePop::POPTYPE_FILEDESTROY
			|| m_enumPopType == basePop::POPTYPE_SYS_SLIM_CLEAN_NEW
			|| m_enumPopType == basePop::POPTYPE_SYS_SLIM_CLEAN_OLD)
        {
            nAction = 3;
        }
        else if(m_enumPopType == basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY)
        {
            nAction = 7;
        }
        ReportPop(nAction);

        //解决某些电脑未将上报数据加入队列中导致上报未成功问题
        Sleep(100);
        EndDialog(-1);
	}
	else
	{
		SetMsgHandled(FALSE);
	}
}

LRESULT KCommonPopDlg::OnMessageAfterInit( UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/ )
{
    AddMenuCommonImpl();
    ResizeDlgCommonImpl();
    return S_OK;
}

CString KCommonPopDlg::GetDlgTextByMemorySize(ULONGLONG ullSize)
{
    CString strDlgText;

    const DWORD dwMaxSize = 1000000;
    if (ullSize >= dwMaxSize)
    {
        strDlgText.Format(L"%d+", dwMaxSize-1);
    }
    else
    {
        strDlgText.Format(L"%d", ullSize);
    }

    return strDlgText;
}

void KCommonPopDlg::ReportPop(int nClick)
{
    switch (m_enumPopType)
    {
    case basePop::POPTYPE_DRIVER_MANAGER:
        KReporter::ReportDriverManagerPop(nClick);
        break;
    case basePop::POPTYPE_FILEDESTROY:
        KReporter::ReportFileDestroyPop(nClick, m_ullSize);
        break;
	case basePop::POPTYPE_SYS_SLIM_CLEAN_OLD:
	case basePop::POPTYPE_SYS_SLIM_CLEAN_NEW:
		{
			const int nVersion = 20;
			ULONGLONG ullSysSize = 0ULL;
			ULONGLONG ullFreeSize = 0ULL;

			cf::disk::SystemDiretoryHelper disk;
			disk.GetSystemDiskSize( ullSysSize, ullFreeSize );

			KReporter::ReportSysSlimCleanPop(m_nSrc,nClick,ullSysSize / 1024,ullFreeSize/1024,m_nType,nVersion,
                SCPT_POP,cf::product::KInfoc_Product_Type::enum_ProductType_Duba,
				GetSystemDiskReleasableSpace(),m_nPopTime);
		}
        break;
    case basePop::POPTYPE_KLSOFTMGR:
        {
            KReporter::ReportKLSoftmgrPop(KLSOFTMGR_POP_TYPE::KPT_NORMAL_POP, nClick,m_strSoftName);
        }
    case basePop::POPTYPE_KLSOFTMGR_ANTIREINSTALL:
        {
            KReporter::ReportKLSoftmgrPop(KLSOFTMGR_POP_TYPE::KPT_ANTIREINSTALL, nClick,m_strSoftName);
        }
        break;
    case basePop::PopType::POPTYPE_KLSOFTMGR_ANTIREINSTALL_RET:
        {
            KReporter::ReportKLSoftmgrPop(KLSOFTMGR_POP_TYPE::KPT_ANTIREINSTALL_RET, nClick,m_strSoftName);
        }
        break;
    case basePop::PopType::POPTYPE_WIN_OPTIMIZE_PRIVACY:
        {
            KReporter::ReportKDoctorPop(1018,nClick,m_strSoftName,m_nNum);
        }
        break;
    default:
        break;
    }
}

UINT KCommonPopDlg::GetXmlResId(basePop::PopType enumPopType)
{
    m_bOldStyle = TRUE;
    UINT uResId = KCOMMON_POP;
    switch (enumPopType)
    {
    case basePop::POPTYPE_DRIVER_MANAGER:
        {
            uResId = KDRIVERMANAGER_POP;
        }
        break;
    case basePop::POPTYPE_FILEDESTROY:
        {
            uResId = KFILEDESTORY_POP;
        }
        break;
    case basePop::POPTYPE_KLSOFTMGR:
        {
            if (KCheckVersionInstance.GetCurrentVersion() 
                == cf::product::enum_ProductType_CleanMaster_cn)
            {
                uResId = ASSOFTMGR_POP;
            }
            else
            {
                uResId = IDR_DLG_KLSOFTMGR_POP;
            }
        }
        break;
    case basePop::POPTYPE_KLSOFTMGR_ANTIREINSTALL:
        {
            uResId = IDR_DLG_KLSOFTMGR_ANTIREINSTALL_POP;
        }
        break;
	case basePop::POPTYPE_SYS_SLIM_CLEAN_OLD:
		{
			uResId = IDR_DLG_SYSSLIM_CLEAN_DLG3;
		}
		break;
	case basePop::POPTYPE_SYS_SLIM_CLEAN_NEW:
		{
			uResId = IDR_DLG_SYSSLIM_CLEAN_DLG2;
            if (KCheckVersionInstance.GetCurrentVersion() == cf::product::enum_ProductType_Duba)
            {
                const int nNewStyle = 2;
                if (nNewStyle == KSDkMagicCubeInstance.GetABTestStrategy(L"ksysslim_pop_style_abtest", 0))
                {
                    m_bOldStyle = FALSE;
                    uResId = IDR_DLG_SYSSLIM_CLEAN_DLG2_NEW;
                }
            }
		}
		break;
    case basePop::POPTYPE_KLSOFTMGR_ANTIREINSTALL_RET:
        {
            uResId = IDR_DLG_KLSOFTMGR_ANTIREINSTALL_POP_RET;
        }
        break;
    case basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY:
        {
            uResId = IDR_DLG_WINOPTIMIZE_PRIVACY_POP;
        }
        break;
    default:
        break;
    }
    return uResId;
}

int KCommonPopDlg::GetStrategyId(LPCTSTR sLayerId)
{
    CPath strDllPath = KCheckVersionInstance.GetCurrentInstalledPath();
    strDllPath.Append(L"kschrodingercat.dll");

    KABTesterLoader loader;
    loader.Load(strDllPath);

    IKABTester* pTest = loader.GetInterface();
    if (pTest != NULL)
    {
        return  pTest->GetStrategyId(sLayerId);
    }

    return 0;
}

void KCommonPopDlg::InitSysSLimPopCommon()
{
	SetItemVisible(IDC_TEXT_SLIMHEADER, false);
	SetItemVisible(IDC_NEW_STYLE_RICH2, false);

	SetItemVisible(IDC_TEXT_SLIMTIP, false);
	SetItemVisible(IDC_NEW_STYLE_RICH, false);
}

void KCommonPopDlg::InitSysSLimPopNew()
{
	SetItemVisible(IDC_NEW_STYLE_RICH, true);

    m_systemThemeStyle = cf::theme::KSystemThemeChangeMonitor::GetSystemTheme();
    m_nType = m_bOldStyle ? KSYSSLIM_POP_STYLE_OLD : (m_systemThemeStyle == cf::theme::DARK ? KSYSSLIM_POP_STYLE_NEW_DARK : KSYSSLIM_POP_STYLE_NEW_LIGHT);

    CString strText1;
    CString strText2;
    //主标题文本
    strText1.Format(BkString::Get(5017), KFunction::GetSysDriveIndex(), GetTxtFromSpaceSize());
    SetRichText(IDC_NEW_STYLE_RICH, strText1);
    //副标题文本
    DWORD dwSoftTrashSize   = KFunction::GetTrashSoftFreeableSize();
    DWORD dwSystemTrashSize = KFunction::GetSysTrashFreeableSize();
    if (dwSoftTrashSize >= 1024 && dwSystemTrashSize >= 1024)
    {
        CString strTemp = L"软件缓存%.1fG，系统缓存%.1fG可清理";
        CString strTextSoftSize;
        CString strTextSystemSize;

        float fSoftGB = (float)dwSoftTrashSize / 1024.00;
        float fSystemGB = (float)dwSystemTrashSize / 1024.00;

        strText2.Format(strTemp, fSoftGB, fSystemGB);
        SetItemText(ID_MAIN_TEXT, strText2);
    }


}

void KCommonPopDlg::InitSysSLimPopOld()
{
    SetItemVisible(IDC_TEXT_SLIMHEADER, true);
    SetItemVisible(IDC_NEW_STYLE_RICH2, true);

    SetItemStringAttribute(IDC_SYS_SLIM_POP_DLG, "class", L"ksysslim_pop_dlg1");
    CString strText1;
    CString strText2;
    DWORD dwFreeableSize = GetSystemDiskReleasableSpace();
    DWORD dwSoftTrashSize = KFunction::GetTrashSoftFreeableSize();
    DWORD dwSystemTrashSize = KFunction::GetSysTrashFreeableSize();
    //主标题文本
    if (dwFreeableSize >= 1024)
    {
        strText1.Format(BkString::Get(5025), KFunction::GetSysDriveIndex(), GetTxtFromSpaceSize());
    }
    else
    {
        strText1 = BkString::Get(5026);
    }
    //副标题文本
    if (dwSoftTrashSize >= 1024 && dwSystemTrashSize >= 1024)
    {
        CString strTextSoftSize;
        CString strTextSystemSize;
      
        float fSoftGB = (float)dwSoftTrashSize / 1024.00;
        strTextSoftSize.Format(L"%.1fG", fSoftGB);
       
        float fSystemGB = (float)dwSystemTrashSize / 1024.00;
        strTextSystemSize.Format(L"%.1fG", fSystemGB);

        strText2.Format(BkString::Get(5024), strTextSoftSize,strTextSystemSize);
    }
    else
    {
        strText2 = BkString::Get(5022);
    }
    SetRichText(IDC_TEXT_SLIMHEADER, strText1);
    SetRichText(IDC_NEW_STYLE_RICH2, strText2);
}

DWORD KCommonPopDlg::GetSystemDiskReleasableSpace()
{
	return KFunction::GetSlimTotalFreeableSize();
}

CString KCommonPopDlg::GetTxtFromSpaceSize()
{
	DWORD dwSize = GetSystemDiskReleasableSpace();
	CString strText;
	if (dwSize >= 1024)
	{
		float fGB = (float)dwSize / 1024.00;
		strText.Format(L"%.1fGB", fGB);
	}
	else
	{
		strText.Format(L"%dMB", dwSize);
	}

	return strText;
}


void KCommonPopDlg::DubaPathAppendFile(CPath& path,LPCTSTR szFileName)
{
	path.m_strPath = KDubaPath::GetDubaInstallPathFromReg();
	path.Append(szFileName);
}

BOOL KCommonPopDlg::ProcessPopTime(cf::BkJson::Document& doc)
{
	if (!doc.HasKey(L"poptime"))
	{
		return FALSE;
	}
 
	CString strPopTime = doc[L"poptime"];
	_stscanf(strPopTime.GetString(), _T("%d"), &m_nPopTime);

	return TRUE;
}


void KCommonPopDlg::SwitchToStyle(cf::theme::SYSTEM_THEME_STYLE style)
{
    CString strText;
    if (style == cf::theme::DARK)
    {
        SetItemAttribute(ID_CLOSE, "skin", "close_dark");
        SetItemAttribute(ID_COMMON_BTN_OK, "skin", "btn_free_dark");
        SetItemAttribute(ID_SETTING_BUTTON, "skin", "drop_down_dark");
        SetItemAttribute(ID_IMG_LOGO, "skin", "logo_duba_white");
        SetItemAttribute(ID_TITLE, "class", "text_title_dark");
        SetItemAttribute(ID_BG, "class", "bg_dark");
        SetItemAttribute(ID_MAIN_TEXT, "class", "text_CFCFCF");

        strText.Format(BkString::Get(127), KFunction::GetSysDriveIndex(), GetTxtFromSpaceSize());
        SetRichText(ID_TIP_TEXT, strText);
    }
    else
    {
        SetItemAttribute(ID_CLOSE, "skin", "close_light");
        SetItemAttribute(ID_COMMON_BTN_OK, "skin", "btn_free_light");
        SetItemAttribute(ID_SETTING_BUTTON, "skin", "drop_down_light");
        SetItemAttribute(ID_IMG_LOGO, "skin", "logo_duba_blue");
        SetItemAttribute(ID_TITLE, "class", "text_title_light");
        SetItemAttribute(ID_BG, "class", "bg_light");
        SetItemAttribute(ID_MAIN_TEXT, "class", "text_7E868E_12");

        strText.Format(BkString::Get(126), KFunction::GetSysDriveIndex(), GetTxtFromSpaceSize());
        SetRichText(ID_TIP_TEXT, strText);
    }
    Invalidate();
}

void KCommonPopDlg::KLSGetExecuteCommandAndParam(CPath& strExe,CString& strParam)
{
    strExe.m_strPath = KCheckVersionInstance.GetCurrentInstalledPath();
    

    switch(m_productType)
    {
    case cf::product::enum_ProductType_CleanMaster_cn:
        {
            strExe.Append(L"kismain.exe");
            strParam = L"/vip:softuninstall /from:5";
        }
        break;
    case cf::product::enum_ProductType_DriverGenius:
        {
            strExe.Append(L"klsoftmgr.exe");
            strParam = L"/from:2";
        }
        break;
    case cf::product::enum_ProductType_Duba:
    default: //> 默认为毒霸
        {
            strExe.Append(L"ksoftmgr.exe");
            strParam = L"/source:2";
        }
        break;
    }
}

void KCommonPopDlg::GetKLSoftMgrPopTxt(CString& txt)
{
    switch(KCheckVersionInstance.GetCurrentVersion())
    {
    case cf::product::enum_ProductType_CleanMaster_cn:
        txt.Format(BkString::Get(123), GetDlgTextByMemorySize(m_ullSize));
        break;
    case cf::product::enum_ProductType_Duba:
    case cf::product::enum_ProductType_DriverGenius:
        txt.Format(BkString::Get(128), GetDlgTextByMemorySize(m_ullSize));
    default:
        break;
    }
}


void KCommonPopDlg::KLSAntiReinstallGetExecuteCommandAndParam(CPath& strExe,CString& strParam)
{

    DWORD dwSoftId;
    CString strSoftName;
    GetParam_SoftMgrAntiReInstall(strSoftName, dwSoftId);

    strExe.m_strPath = KCheckVersionInstance.GetCurrentInstalledPath();

    if (KCheckVersionInstance.IsDubaVersion())
    {
        strExe.Append(L"ksoftmgr.exe");
        strParam.Format(L"/source:5 /clean:2 /acid:%u", dwSoftId);
    }
    else
    {
        strExe.Append(L"klsoftmgr.exe");
        strParam.Format(L"/from:5 /clean:2 /acid:%u", dwSoftId);
    }
}


void KCommonPopDlg::KLSAntiReinstallShowSettingGetExecuteCommandAndParam(CPath& strExe,CString& strParam)
{
    DWORD dwSoftId;
    CString strSoftName;
    GetParam_SoftMgrAntiReInstall(strSoftName, dwSoftId);

    strExe.m_strPath = KCheckVersionInstance.GetCurrentInstalledPath();

    if (KCheckVersionInstance.IsDubaVersion())
    {
        strExe.Append(L"ksoftmgr.exe");
		const int nShowFromAntiReInstallPop = 122;
		strParam.Format(L"/showantisetting /acid:%u /source:%d", dwSoftId,nShowFromAntiReInstallPop);
    }
    else
    {
        strExe.Append(L"klsoftmgr.exe");
        strParam.Format(L"/from:8 /showantisetting /acid:%u", dwSoftId);
    }
}

void KCommonPopDlg::AddMenuCommonImpl()
{
    MenuInit();
    switch (m_enumPopType)
    {
    case basePop::POPTYPE_KLSOFTMGR:
        AddMenuItem(L"30天不再弹出", MENU_ID::MI_NOT_REMIND_AGAIN);
        break;
    case basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY:  // 不另外添加菜单按钮子选项
        break;
    default:
        AddMenuItem(L"不再弹出", MENU_ID::MI_NOT_REMIND_AGAIN);
        break;
    }
    SetFocus();
}


void KCommonPopDlg::MenuInit()
{
    m_settingMenu.CreateEx();
    m_settingMenu.AddLisener(this);
    m_settingMenu.SetItemWidthAndHeight(84);
    m_settingMenu.SetWindowPos(HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE);
}

void KCommonPopDlg::AddMenuItem(LPCWSTR lpszTxt, MENU_ID eId)
{
    MenuItem item;
    item.strTitle = lpszTxt;
    item.id = eId;
    m_settingMenu.AddItem(item);
}

void KCommonPopDlg::ResizeDlgCommonImpl()
{
    int nWidth = 470, nHeight = 170;
    switch (m_enumPopType)
    {
    case basePop::POPTYPE_DRIVER_MANAGER:
    case basePop::POPTYPE_SYS_SLIM_CLEAN_OLD:
        {
            nWidth = 320;
            nHeight = 390;
        }
        break;
    case basePop::POPTYPE_SYS_SLIM_CLEAN_NEW:
        {
            if (m_bOldStyle)
            {
                nWidth = 534;
                nHeight = 233;
            }
            else
            {
                nWidth = 364;
                nHeight = 170;
            }
        }
        break;
    case basePop::POPTYPE_KLSOFTMGR:
        {
            if (KCheckVersionInstance.GetCurrentVersion()
                != cf::product::enum_ProductType_CleanMaster_cn)
            {
                nWidth = 364;
                nHeight = 170;
            }
        }
        break;
    case basePop::POPTYPE_KLSOFTMGR_ANTIREINSTALL:
    case basePop::POPTYPE_KLSOFTMGR_ANTIREINSTALL_RET:
        {
            nWidth = 360;
            nHeight = 180;
        }
        break;
    case basePop::POPTYPE_WIN_OPTIMIZE_PRIVACY:
        {
            nWidth = 364;
            nHeight = 170;
        }
        break;
    default:
        break;
    }

    ResiteDlgCommonImpl(nWidth, nHeight);
}


void KCommonPopDlg::ResiteDlgCommonImpl(int nWidth, int nHeight)
{
    CRect rcSrc(0, 0, nWidth, nHeight), rcDest;
    kplanet::Poputils::GetPosition(rcSrc, rcDest, kplanet::Poputils::rightbottom);

    float fRate = BKWinDpiAware::GetInstance().GetDpiScaleRate();

    MoveWindow(rcDest.left, rcDest.top, rcDest.Width() * fRate, rcDest.Height() * fRate);
    SetWindowPos(HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE);
}


void KCommonPopDlg::OnBtnCancelAntiReinstallRetPopImpl()
{
    DWORD dwSoftId = 0;
    CString strSoftName;
    GetParam_SoftMgrAntiReInstall(strSoftName, dwSoftId);

    if (dwSoftId > 0)
    {
        CString strSoftId;
        strSoftId.Format(L"%u", dwSoftId);
        KPopCommFunc::GetInstance()->SetPopSpecailDWORDValue(GetPopName(), strSoftId, FALSE);
    }

    static const int nClickSoftNeverTip = 3;
    ReportPop(nClickSoftNeverTip);

    Sleep(100);
	EndDialog(-1);
}


bool KCommonPopDlg::CanAntiReinstallRetPopShow()
{

        if (KPopCommFunc::GetInstance()->GetPopNoShowAnymore(GetPopName()))
    { //> 设置了不再弹出
        return false;
    }

    DWORD dwSoftId = 0;
    CString strSoftName;
    GetParam_SoftMgrAntiReInstall(strSoftName, dwSoftId);
    
    if (dwSoftId <= 0 || strSoftName.IsEmpty())
    { //> 软件ID或软件名称无法识别的一律不弹泡泡
        return false;
    }

    CString strSoftId;
    strSoftId.Format(L"%u", dwSoftId);
    return KPopCommFunc::GetInstance()->GetPopSpecailDWORDValue(GetPopName(), strSoftId, TRUE);
}


void KCommonPopDlg::OnBtnCancelAntiReinstallPopImpl()
{
	DWORD dwSoftId = 0;
	CString strSoftName;
	GetParam_SoftMgrAntiReInstall(strSoftName, dwSoftId);

	if (dwSoftId > 0)
	{
		CString strSoftId;
		strSoftId.Format(L"%u", dwSoftId);
		KPopCommFunc::GetInstance()->SetPopSpecailDWORDValue(GetPopName(), strSoftId, FALSE);
	}

	static const int nClickSoftNeverTip = 3;
	ReportPop(nClickSoftNeverTip);

	Sleep(100);
	EndDialog(-1);
}

bool KCommonPopDlg::CanAntiReinstallPopShow()
{
	DWORD dwSoftId = 0;
	CString strSoftName;
	GetParam_SoftMgrAntiReInstall(strSoftName, dwSoftId);

	if (dwSoftId <= 0 || strSoftName.IsEmpty())
	{ //> 软件ID或软件名称无法识别的一律不弹泡泡
		return false;
	}

	CString strSoftId;
	strSoftId.Format(L"%u", dwSoftId);
	return KPopCommFunc::GetInstance()->GetPopSpecailDWORDValue(GetPopName(), strSoftId, TRUE);
}

void KCommonPopDlg::InitDriverManagerUi(CString &strText)
{
    CBkPanel* pWindow = (CBkPanel*)GetBkItem(ID_TEXT_INFO);

    std::string strXml;
    int nTop = 0;
    const int nItemSpaceLength = 50;  // 各个项之间的间距
    CStringA strPos;
    for (int i = 0; i < m_vecDeviceInfo.size() && i < DRIVER_MANAGER_SHOW_MAX_ITEM; ++i)
    {
        CString strType, strName, strIcon;
        for (int j = 0; j < _countof(drivermanager::g_aDeviceDataSet); ++j)
        {
            if (0 == (m_vecDeviceInfo[i].strType.CompareNoCase(drivermanager::g_aDeviceDataSet[j].lpszType)))
            {
                strName = drivermanager::g_aDeviceDataSet[j].lpszName;
                strIcon = drivermanager::g_aDeviceDataSet[j].lpszIconFileName;
                int index = strIcon.Find(L".");
                if (index != -1)
                {
                    strIcon = strIcon.Mid(0, index);
                }
                break;
            }
        }

        if (strName.IsEmpty() || strIcon.IsEmpty())
        {
            strName = L"其他设备";
            strIcon = L"other";
        }

        strPos.Format("0,%d,-0,%d", nTop, nTop + nItemSpaceLength);
        std::ostringstream stringStream;
        std::string strSubAttr = "可更新驱动版本:";
        if (m_productType == cf::product::enum_ProductType_DriverGenius)
        {
            strSubAttr = "驱动版本:";
        }
        stringStream << "<dlg pos=\"" << strPos << "\">" \
            "<img pos=\"20,4\"  skin=\"" << cf::string::SysWideToNativeMB((LPWSTR)(LPCWSTR)strIcon) << "\" sub=\"0\"/>" \
            "<text pos=\"62,0,-0,18\" class=\"text_333333_14\">" << cf::string::SysWideToNativeMB((LPWSTR)(LPCWSTR)strName) << ":" << cf::string::SysWideToNativeMB((LPWSTR)(LPCWSTR)m_vecDeviceInfo[i].strName) << "</text>" \
            "<text pos=\"62,24,-0,38\" class=\"text_11A7FE_12\">" << strSubAttr << cf::string::SysWideToNativeMB((LPWSTR)(LPCWSTR)m_vecDeviceInfo[i].strUpdateVersion) << "</text>" \
            "<hr pos=\"20,-5,-20,-5\" crbg=\"EEEFF2\"/>" \
            "</dlg>";
        strXml = stringStream.str();
        TiXmlElement pTiXmlChildElem("");
        pTiXmlChildElem.Parse(cf::string::NativeMBToUTF8(strXml).c_str() , NULL, TIXML_ENCODING_UTF8);
        pWindow->AppendChild(&pTiXmlChildElem);
        nTop += nItemSpaceLength;
    }
    int nPosStart = 155 + nTop + 10;
    const int nBtnMoreHeigth = 24;
    strPos.Format("120,%d,200,%d", nPosStart, nPosStart + nBtnMoreHeigth);
    SetItemAttribute(ID_BTN_SHOW_MORE, "pos", strPos);

    UINT uTextId = 109;

    switch (m_productType)
    {
    case cf::product::enum_ProductType_Duba:
        SetItemVisible(ID_VIP_FLAG, TRUE);
        break;
    case cf::product::enum_ProductType_DriverGenius:
        SetItemText(ID_TITLE, L"驱动精灵-驱动检测");
        SetItemText(ID_MAIN_TEXT, L"修复驱动问题，保持电脑稳定运行");
        SetItemText(ID_COMMON_BTN_OK, L"立即修复");
        SetItemAttribute(ID_BG, "skin", "drivermanager_bg_dg");
        uTextId = 111;
        break;
    default:
        break;
    }
    strText.Format(BkString::Get(uTextId), GetDlgTextByMemorySize(m_ullSize));
}


void KCommonPopDlg::InitKLSoftMgrUiCommon()
{
    bool bIsDuba = cf::product::enum_ProductType_Duba == m_productType;
    SetItemVisible(ID_IMG_LOGO, bIsDuba);
    SetItemVisible(ID_IMG_SDK_LOGO, !bIsDuba);
}

void KCommonPopDlg::GetParam_SoftMgrAntiReInstall(CString& strSoftName, DWORD& dwSoftId)
{
    cf::BkJson::Document doc;
    if (!doc.LoadString(m_strParamJson))
        return ;

    if (doc.HasKey(L"softname"))
    {
        strSoftName = doc[L"softname"];
    }

    if (doc.HasKey(L"softid"))
    {
        CString strSoftId = doc[L"softid"];
        if (!strSoftId.IsEmpty())
        {
            _stscanf(strSoftId.GetString(), _T("%u"), &dwSoftId);
        }
        else
        {
            dwSoftId = (ULONG)doc[L"softid"];
        }
    }
}
