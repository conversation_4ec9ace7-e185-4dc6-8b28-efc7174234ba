#pragma once

class KPlanetMainDlg : public CBkDialogViewEx, public IShadowPaintHook
{
public:
    KPlanetMainDlg(kplanet::KPopTask* pHost);
    ~KPlanetMainDlg(void);

    enum enumResID{
        ID_CLOSE = 100,
        ID_MAIN_BUTTON = 101,
    };

public:
    BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
        BK_NOTIFY_ID_COMMAND(ID_CLOSE, OnBtnClose)
        BK_NOTIFY_ID_COMMAND(ID_MAIN_BUTTON, OnBtnClick)
        BK_NOTIFY_MAP_END()

        BEGIN_MSG_MAP_EX(KPlanetMainDlg)
        MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
        MSG_WM_INITDIALOG(OnInitDialog)
        CHAIN_MSG_MAP(CBkDialogViewEx)
        END_MSG_MAP()

public:
    virtual BOOL NotifyShadowPaint(HDC hDC, CRect& rct);
    LRESULT OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/);
    void OnBtnClose();
    void OnBtnClick();
    void OpenVipCenter();

protected:
    kplanet::KPopTask* m_pHost;
    bool is_expired;
};
