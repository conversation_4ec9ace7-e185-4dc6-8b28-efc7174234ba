#ifndef __KDUBA_SAFEAPIHELPER_H__
#define __KDUBA_SAFEAPIHELPER_H__

#include "ISafeApiHelper.h"

class KSafeApiHelper : public ISafeApiHelper
{
public:
    KSafeApiHelper() {}
    ~KSafeApiHelper() {}

    BOOL InitKsapi();
    void UnInitKsapi();
    BOOL IsInit();

    LONG KRegCreateKeyEx(HKEY hKey, LPCTSTR lpSubKey, DWORD Reserved, LPTSTR lpClass,  DWORD dwOptions,  REGSAM samDesired,
        LPSECURITY_ATTRIBUTES lpSecurityAttributes,  PHKEY phkResult, LPDWORD lpdwDisposition);

    LONG KRegOpenKeyEx(HKEY hKey, LPCTSTR lpSubKey, DWORD ulOptions, REGSAM samDesired, PHKEY phkResult);
    LONG KRegSetValueEx(<PERSON>E<PERSON> hKey, LPCTSTR lpValueName, DWORD Reserved, DWORD dwType, const BYTE* lpData, DWORD cbData);
    LONG KRegCloseKey(HKEY hKey);
};

#endif