 #pragma once
#include "KPopBase.h"
#include "CBkMenu.h"

#include <atlpath.h>

class KSoftDeepCleanPopDlg : public CBkDialogViewEx,
	public IShadowPaintHook, public KPopBase, public BKMenuCallBack
{
public:
    KSoftDeepCleanPopDlg(kplanet::KPopTask* pHost);
    ~KSoftDeepCleanPopDlg(void);

#define UM_MSG_AFTERINIT   (WM_USER + 1)

    enum enumResID{
        ID_CLOSE = 100,
		ID_CLEAN_BUTTON = 101,
        ID_SETTING_BUTTON = 102,
        ID_TIP_TEXT = 103,
        ID_HEADER = 109,
        ID_BG = 110,
    };

    enum {
        ID_DEFAULT_OPEN = 0,
        ID_CLICK_CLEAN = 1,
        ID_CLICK_CLOSE = 2,
        ID_CLICK_AUTO_CLOSE = 3,
        ID_CLICK_NEVER = 4,
    };

public:
    BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
        BK_NOTIFY_ID_COMMAND(ID_CLOSE, OnBtnClose)
        BK_NOTIFY_ID_COMMAND(ID_SETTING_BUTTON, OnBtnSetting)
        BK_NOTIFY_ID_COMMAND(ID_CLEAN_BUTTON, OnBtnClean)
        BK_NOTIFY_MAP_END()

        BEGIN_MSG_MAP_EX(KSoftDeepCleanPopDlg)
        MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
        MSG_WM_INITDIALOG(OnInitDialog)
        MESSAGE_HANDLER(UM_MSG_AFTERINIT, OnMessageAfterInit)
        MSG_WM_TIMER(OnTimer)
        CHAIN_MSG_MAP(CBkDialogViewEx)
        END_MSG_MAP()

public:
    virtual BOOL Init(CString strJsonParam);
    virtual BOOL UnInit();
    virtual DWORD GetPopId(){return ksoftdeepclean_pop_id;}
    virtual CString GetPopName(){return ksoftdeepclean_pop_name;}
    virtual BOOL CanShow();
    virtual BOOL Show();

    virtual BOOL NotifyShadowPaint(HDC hDC, CRect& rct);
	virtual void MenuItemClick(int menuId);
	LRESULT OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/);
	void OnTimer(UINT_PTR nIDEvent);
    void OnBtnClose();
    void OnBtnSetting();
    void OnBtnClean();
    LRESULT OnMessageAfterInit(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/);
    
private:
    void AddMenuTask(LPCTSTR lpszText, int id, LPCTSTR lpszTip = NULL, LPCTSTR lpszIconSkin = NULL);
    void SetTipDlgInfo(ULONGLONG ullTrashSize);
    void CreateMenu();
    void SetStyleByCurrentProduct();
    void ReportClick(DWORD dwClick);
    bool GetExcutePathAndParam(CPath& strPath, CString& strParam);
    CString SizeToString(ULONGLONG ullSize);
    DWORD FloatFormat( float f, float &fout );

protected:
    kplanet::KPopTask* m_pHost;
	CBKMenu m_settingMenu;
    ULONGLONG m_ullSize;
    DWORD m_dwSoftId;
    CString m_strSoftName;
};
