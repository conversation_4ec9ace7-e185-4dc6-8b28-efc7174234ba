#include "StdAfx.h"
#include "KVipFreePackPopDlg.h"

#include "duba_include\purevip\pure_vip_switch.h"
#include "duba_include/purevip/knewvip_wrapper.hpp"

#include "include\framework\KRegister2.h"
#include "publish\kvip\getvipinfowarpper.h"
#include "include\productinfo\KQQpcmProductInfo.h"
#include "include\productinfo\KNUMProductInfo.h"
#include "include\productinfo\KHuoRongProductInfo.h"
#include "include\framework\KFileVersion.h"
#include "cf_public/core/time/KTime.h"
#include "duba_include\purevip\pure_vip_wrapper.hpp"
#include "duba_include\purevip\KVipUsageRecord.h"
#include "cf_public/core/win_version/KSystemVersion.h"

static const wchar_t kVipFreePackPopRegPath[] = L"SOFTWARE\\kingsoft\\antivirus\\recommend\\pure_vip_freepack_pop";

KVipFreePackPopDlg::KVipFreePackPopDlg(kplanet::KPopTask* pHost) : 
CBkDialogViewEx(VIP_FREEPACK_POP), m_pHost(pHost)
{
    EnableShadow(TRUE);
    SetDrawBorder(FALSE);
    SetShadowSize(28);
    SetShadowPaintHook(this);
}

KVipFreePackPopDlg::~KVipFreePackPopDlg(void)
{
}

BOOL KVipFreePackPopDlg::Init(CString strJsonParam)
{
	cf::BkJson::Document doc;
	if (!doc.LoadString(strJsonParam))
		return FALSE;

    return TRUE;
}

BOOL KVipFreePackPopDlg::UnInit()
{
    return TRUE;
}

BOOL KVipFreePackPopDlg::CanShow()
{
    BOOL bRet = FALSE; 
 
    do 
    {
        pure_vip::UserInfo user_info;
        pure_vip::PureVipWrapper::GetInstance()->GetUserInfo(user_info);
        KRegister2 reg;

        if (!reg.Open(HKEY_CURRENT_USER, kVipFreePackPopRegPath))
        {
            bRet = TRUE;
            break;
        }

        DWORD show_timestamp = 0;
        if (!reg.Read(user_info.open_id.c_str(), show_timestamp) || show_timestamp == 0)
        {
            bRet = TRUE;
            break;
        }

    } while (false);
 
    pure_vip::UserInfo user_info;
    pure_vip::PureVipWrapper::GetInstance()->GetUserInfo(user_info);
    KReporter::ReportNewvipPopShow(4, bRet, L"", user_info.open_id.data(), 0);

    return bRet;
}

BOOL KVipFreePackPopDlg::Show()
{
    pure_vip::UserInfo user_info;
    pure_vip::PureVipWrapper::GetInstance()->GetUserInfo(user_info);
    KRegister2 reg;
    if (reg.Open(HKEY_CURRENT_USER, kVipFreePackPopRegPath, FALSE))
    {
        reg.Write(user_info.open_id.c_str(), time(0));
    }

	DoModal();
    return TRUE;
}

BOOL KVipFreePackPopDlg::NotifyShadowPaint(HDC hDC, CRect& rct)
{
    WINDOWPOS wndPos = {0};
    CBkExpendImg exmPandImg;

    wndPos.x = rct.left;
    wndPos.y = rct.top;
    wndPos.cx = rct.Width();
    wndPos.cy = rct.Height();

    exmPandImg.SetAttribute("pos", "0,0,-0,-0", TRUE);
    exmPandImg.BkSendMessage(WM_WINDOWPOSCHANGED, NULL, (LPARAM)&wndPos);
    exmPandImg.SetAttribute("margin_left", "28", TRUE);
    exmPandImg.SetAttribute("margin_right", "28", TRUE);
    exmPandImg.SetAttribute("margin_top", "28", TRUE);
    exmPandImg.SetAttribute("margin_bottom", "28", TRUE);
    exmPandImg.SetAttribute("skin", "shadow", TRUE);
    exmPandImg.BkSendMessage(WM_PAINT, (WPARAM)hDC);
    return FALSE;
}

LRESULT KVipFreePackPopDlg::OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/)
{
	cf::BkJson::Document jsonParam;
	ModifyStyleEx(0, WS_EX_TOOLWINDOW);

	if ( cf::win_ver::KSystemVersion::GetSystemVersion() == cf::win_ver::KSystemVersion::enumSystemVersionWinXp )
    {
        SetItemAttribute(TXT_EXPIRED, "pos", "220,42,-0,-0");
    }

    return TRUE;
}

void KVipFreePackPopDlg::OnBtnClose()
{
    pure_vip::UserInfo user_info;
    pure_vip::PureVipWrapper::GetInstance()->GetUserInfo(user_info);
    KReporter::ReportNewvipPopResult(4, 2, user_info.open_id.data(), 0);

    m_pHost->SetHideReason(KPopCenterDef::PopHideReasonCode_ClickClose);
    EndDialog(-1);
}

void KVipFreePackPopDlg::OnBtnClick()
{
    const purevip::eVipFrom VipFrom = (purevip::eVipFrom)1008;
    purevip::CallVipInstance(NULL, purevip::eOpenPayPage, NULL, NULL, NULL, VipFrom);

    pure_vip::UserInfo user_info;
    pure_vip::PureVipWrapper::GetInstance()->GetUserInfo(user_info);
    KReporter::ReportNewvipPopResult(4, 1, user_info.open_id.data(), 0);

    m_pHost->SetHideReason(KPopCenterDef::PopHideReasonCode_ClickBtn);
    EndDialog(-1);
}
