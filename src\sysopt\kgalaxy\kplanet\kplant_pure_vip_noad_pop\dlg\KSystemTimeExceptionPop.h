 #pragma once
#include "KPopBase.h"

class KSystemTimeExceptionPop 
	: public CBkDialogViewEx
	, public IShadowPaintHook
    , public KPopBase
{
public:
    KSystemTimeExceptionPop(kplanet::KPopTask* pHost);
    ~KSystemTimeExceptionPop(void);

#define UM_MSG_AFTERINIT   (WM_USER + 1)

	enum enumResID{
		ID_TEXT_NEVER_SHOW = 120,
		ID_BTN_CLOSE = 121,
		ID_BTN_OK = 122,
		ID_TEXT_DIFF_TIME = 123,
	};

public:
	BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
		BK_NOTIFY_ID_COMMAND(ID_TEXT_NEVER_SHOW, OnBtnNeverShow)
		BK_NOTIFY_ID_COMMAND(ID_BTN_CLOSE, OnBtnClose)
		BK_NOTIFY_ID_COMMAND(ID_BTN_OK, OnBtnOK)
		BK_NOTIFY_MAP_END()

		BEGIN_MSG_MAP_EX(KSystemTimeExceptionPop)
		MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
		MSG_WM_INITDIALOG(OnInitDialog)
		MSG_WM_TIMER(OnTimer)
		MSG_WM_MOUSEMOVE(OnMouseMove)
		MESSAGE_HANDLER(UM_MSG_AFTERINIT, OnMessageAfterInit)
		CHAIN_MSG_MAP(CBkDialogViewEx)
		END_MSG_MAP()

protected: 
	//public KPopBase
    virtual BOOL Init(CString strJsonParam);
    virtual BOOL UnInit();
    virtual DWORD GetPopId();
    virtual CString GetPopName();
    virtual BOOL CanShow();   
    virtual BOOL Show();

   // public IShadowPaintHook
	virtual BOOL NotifyShadowPaint(HDC hDC, CRect& rct);


protected:
	LRESULT OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/);
	LRESULT OnMessageAfterInit(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/);
	void OnTimer(UINT_PTR nIDEvent);
	void OnMouseMove(UINT nFlags, CPoint point);

	void OnBtnClose();
	void OnBtnNeverShow();
	void OnBtnOK();

private:
	void _AutoClose();
	void OnEndDlgImpl(UINT uRetCode);
    void _Report(int action);
	BOOL _IsClickedNeverShow();
	CString _GetShowDiffTimeText(__time64_t nDiffTime);
	void ResiteDlgCommonImpl(int nWidth, int nHeight);
	void _UpdateUI();
	void _GotoFix();
    

private:
    kplanet::KPopTask * m_pHost;
	__time64_t m_nDiffTime;
	DWORD m_dwMouseMoveLastTick;
};
