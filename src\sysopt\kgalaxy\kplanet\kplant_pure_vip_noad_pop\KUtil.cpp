#include "StdAfx.h"
#include "KUtil.h"

#include "cf_public/component/product/KCheckVersion.h"
#include "duba_include/purevip/vip_gui_wrapper.hpp"
#include "duba_include/purevip/knewvip_wrapper.hpp"

namespace util {


    void UnionRelogin()
    {
        CPath vipCenterPath(cf::product::KCheckVersion::Instance().GetCurrentInstalledPath());
        if (cf::product::KCheckVersion::Instance().GetCurrentVersion() == cf::product::enum_ProductType_Duba)
        {
            vipCenterPath.Append(L"knewvip.exe");
            purevip::CallVipInstance(vipCenterPath.m_strPath, purevip::eOpenLoginPage, NULL, NULL, NULL,
                purevip::eVipFrom_Machine_Limit_Pop);
        } 
        else
        {
            vipCenterPath.Append(L"kvipgui.exe");
            vip_sdk::CallVipInstance(vipCenterPath.m_strPath, vip_sdk::eOpenLoginPage, NULL, NULL, NULL,
                purevip::eVipFrom_Machine_Limit_Pop);
        }
    }

    CSize CaculateTxtLength(LPCWSTR szTxt, int nLength, HFONT hFont, BOOL  bGdiPlus /*= FALSE*/)
    {
        CSize     szRet;
        HDC       hdc = ::GetDC(NULL);
        CDCHandle dc(hdc);

        if (dc.m_hDC == NULL)
            return szRet;

        if (bGdiPlus == FALSE)
        {
            RECT  rc       = {0};
            HFONT hFontOld = dc.SelectFont(hFont);

            dc.DrawText(szTxt, nLength, &rc, DT_CALCRECT | DT_SINGLELINE);
            dc.SelectFont(hFontOld);
            szRet.cx = rc.right - rc.left + 5;
            szRet.cy = rc.bottom - rc.top;
        }
        else
        {
            Gdiplus::StringFormat format;
            Gdiplus::Graphics     graphics(dc);
            BkGdiplusFont         font(dc, hFont);
            Gdiplus::SizeF        layoutSize(999, 30);
            format.SetAlignment(Gdiplus::StringAlignmentFar);

            Gdiplus::SizeF stringSize;
            graphics.MeasureString(szTxt, nLength, font, layoutSize, &format, &stringSize);

            szRet.cx = stringSize.Width + 1;
            szRet.cy = stringSize.Height + 1;
        }

        dc.Detach();
        ::ReleaseDC(NULL, hdc);

        return szRet;
    }

    CString TrimStringImpl(const CString &str, WORD uFontKey, int nMaxLen, BOOL  bGdiPlus /*= FALSE*/)
    {
        CString strRet;
        CString strDrawText(str);

        int nCount      = 0;
        int nTotalCount = strDrawText.GetLength();
        int nDrawCount  = nTotalCount;

        BOOL bMax = FALSE;

        while (nDrawCount > 0)
        {
            strDrawText = str.Mid(0, nDrawCount);
            if (bMax)
            {
                strDrawText.Append(L"...");
            }

            if (CaculateTxtLength(
                strDrawText, strDrawText.GetLength(),
                BkFontPool::GetFont(uFontKey), bGdiPlus).cx
                <= nMaxLen)
            {
                strRet = strDrawText;
                break;
            }
            else
            {
                bMax = TRUE;
            }

            nDrawCount--;
        }

        return strRet;
    }

    void ConvertSysEnvironmentPath(CString& strPath)
    {
        int nNextIndex = 0;
        while (true) 
        {
            int nIndex1 = strPath.Find(L'%', nNextIndex);
            if(nIndex1 == -1) 
            {
                break;
            }

            int nIndex2 = strPath.Find(L'%', nIndex1 + 1);
            if(nIndex2 == -1) 
            {
                break;
            }

            CString strToConvet = strPath.Mid(nIndex1, nIndex2 - nIndex1 + 1);

            WCHAR szPath[MAX_PATH] = {0};
            ::ExpandEnvironmentStrings(strToConvet, szPath, MAX_PATH);

            CString strTemp(szPath);

            CString strNewPath = strPath.Mid(0, nIndex1) + strTemp;
            nNextIndex = strNewPath.GetLength();

            strPath =  strNewPath + strPath.Mid(nIndex2 + 1);
        }

        strPath.Replace(L"//", L"\\");
        strPath.Replace(L"\\\\", L"\\");
        strPath.MakeLower();
    }

}