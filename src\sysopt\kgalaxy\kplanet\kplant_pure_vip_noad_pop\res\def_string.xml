﻿<string>
  <s id="100">
    <![CDATA[
    <text class="richtext_base_text">发现</text>
    <offset length="2"></offset>
    <text class="richtext_number">%d</text>
    <offset length="2"></offset>
    <text class="richtext_base_text">款软件广告未拦截，请及时拦截！</text>
  ]]>
  </s>
  <s id="101">
    <![CDATA[
    <text class="richtext_base_text">小豹已为您服务</text>
    <offset length="2"></offset>
    <text class="richtext_number_6CC000">%d</text>
    <offset length="2"></offset>
    <text class="richtext_base_text">天，</text>
  ]]>
  </s>
  <s id="102">
    <![CDATA[
    <text class="richtext_base_text">已累计为您拦截</text>
    <offset length="2"></offset>
    <text class="richtext_number_ED0D27">%d</text>
    <offset length="2"></offset>
    <text class="richtext_base_text">条广告！</text>
  ]]>
  </s>
  
  <s id="103">
    <![CDATA[
		<text class="richtext_base_text">亲，您的账号已绑定另外</text>
		<offset length="2"></offset>
        <text class="richtext_number_ED0D27">%d</text>
		<offset length="2"></offset>
		<text class="richtext_base_text">台电脑，是否授权当前</text>
		<break/>
		<text class="richtext_base_text">电脑使用，授权后其他设备失效？</text>
	]]>
  </s>
  
  <s id="104">
    <![CDATA[
		<text class="richtext_base_text">亲，您的账号已绑定电脑数已超出</text>
		<offset length="2"></offset>
        <text class="richtext_number_ED0D27">%d</text>
		<offset length="2"></offset>
		<text class="richtext_base_text">台限制，继续</text>
		<break/>
		<text class="richtext_base_text">使用请点击“电脑管理”删除无效电脑。</text>
	]]>
  </s>
  
   <s id="105">
    <![CDATA[
		<text class="richtext_base_text2">尊敬的会员，您的账号已在另外</text>
		<offset length="2"></offset>
        <text class="richtext_base_text2">%d</text>
		<offset length="2"></offset>
		<text class="richtext_base_text2">台设备登录，本机被迫下线。</text>
	]]>
  </s>
  
  <s id="106">
    <![CDATA[
		<text class="richtext_base_text2">尊敬的会员，您的账号已超出</text>
		<offset length="2"></offset>
        <text class="richtext_base_text2">%d</text>
		<offset length="2"></offset>
		<text class="richtext_base_text2">台设备登录限制，本机将被迫下线。</text>
	]]>
  </s>
  
  <s id="107">
    <![CDATA[
		<text class="richtext_base_text3">磁盘有</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF1223">%s</text>
		<offset length="2"></offset>
		<text class="richtext_base_text3">个碎片未清理</text>
	]]>
  </s>
  
  <s id="108">
    <![CDATA[
		<text class="richtext_base_text3">微信有</text>
		<offset length="2"></offset>
    <text class="richtext_number_FF1223">%s</text>
		<offset length="2"></offset>
		<text class="richtext_base_text3">文件可释放</text>
	]]>
  </s>

  <s id="109">
    <![CDATA[
		<text class="text_FFE26E_16">%s</text>
		<offset length="2"></offset>
    <text class="text_FFFFFF_16">个驱动需要更新</text>
	]]>
  </s>

  <s id="110">
    <![CDATA[
    <text class="richtext_333333_17">回收站有</text>
    <text class="text_EF7319_18">%s</text>
    <text class="richtext_333333_17">个文件可粉碎</text>
	]]>
  </s>

  <s id="111">
    <![CDATA[
		<text class="text_FFE26E_16">%s</text>
		<offset length="2"></offset>
    <text class="text_FFFFFF_16">个驱动问题需要修复</text>
	]]>
  </s>

  <s id="112">
    <![CDATA[
    <text class="text_FFFFFF_16_28">发现</text>
		<text class="text_FDFC30_20" adjust="-3">%I64d</text>
    <text class="text_FFFFFF_16_28">个硬件驱动异常</text>
	]]>
  </s>

  <s id="113">
    <![CDATA[
    <text class="text_FFFFFF_16_28">发现</text>
		<text class="text_FDFC30_20" adjust="-3">%I64d</text>
    <text class="text_FFFFFF_16_28">个硬件驱动需要更新</text>
	]]>
  </s>

  <s id="114">
    <![CDATA[
    <text class="text_FFFFFF_16_28">发现</text>
		<text class="text_FDFC30_20" adjust="-3">%I64d</text>
    <text class="text_FFFFFF_16_28">个驱动异常,</text>
    <text class="text_FDFC30_20" adjust="-3">%I64d</text>
    <text class="text_FFFFFF_16_28">个驱动更新</text>
	]]>
  </s>

  <s id="115">修复后可提升电脑性能，避免硬件故障</s>

  <s id="116">更新后可提升电脑性能，避免硬件故障</s>

  <s id="117">不修复</s>

  <s id="118">立即修复</s>

  <s id="119">不更新</s>

  <s id="120">立即更新</s>
	
	<s id="121">
	<![CDATA[
	<text class="text_FA5944_22_bold" adjust="-4">%I64d</text>
	<offset length="1"></offset>
    <text class="text_FFFFFF_16">个驱动需要更新</text>
	]]>
	</s>
	

	<s id="157">
		<![CDATA[
	<text class="text_FFFFFF_16">%s驱动需要更新</text>
	]]>
	</s>
	
	
	
  <s id="5017">
    <![CDATA[
	<text class="richtext_text_bottom">%s存在</text>
    <offset length="1"></offset>
    <text class="richtext_number_FF0000">%s</text>
    <offset length="1"></offset>
    <text class="richtext_text_bottom">空间可释放</text>
	]]>
  </s>

  <s id="5021">
    <![CDATA[
	  <text class="richtext_text_bottom_FFFFFF">有</text>
    <offset length="1"></offset>
    <text class="richtext_number_EBDE34_small">%s</text>
    <offset length="1"></offset>
    <text class="richtext_text_bottom_FFFFFF">空间可释放, 加速电脑</text>
	  ]]>
  </s>
  <s id="5022">
    <![CDATA[
        <text class="richtext_text_bottom_FFFFFF">可能导致系统异常，建议释放磁盘空间</text>
    ]]>
  </s>
	
  <s id="5024">
    <![CDATA[
    <text class="richtext_text_bottom_FFFFFF">软件缓存</text>
    <offset length="1"></offset>
    <text class="richtext_number_EBDE34_small">%s</text>
    <offset length="1"></offset>
    <text class="richtext_text_bottom_FFFFFF">,系统缓存</text>
    <offset length="1"></offset>
    <text class="richtext_number_EBDE34_small">%s</text>
    <offset length="1"></offset>
    <text class="richtext_text_bottom_FFFFFF">可清理</text>
    ]]>
  </s>

  <s id="5025">
    <![CDATA[
    <text class="slim_pop_header">%s存在</text>
    <offset length="1"></offset>
    <text class="richtext_number_EBDE34_big">%s</text>
    <offset length="1"></offset>
    <text class="slim_pop_header">空间可释放</text>
    ]]>
  </s>

  <s id="5026">
    <![CDATA[
    <text class="slim_pop_header">C盘剩余空间严重不足</text>
    ]]>
  </s>
	 
	

  <s id="124">
    <![CDATA[
		<text class="richtext_base_text3">磁盘有</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>
		<offset length="2"></offset>
		<text class="richtext_base_text3">个碎片未清理</text>
	]]>
  </s>

  <s id="125">
    <![CDATA[
		<text class="richtext_FFFFFF">磁盘有</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>
		<offset length="2"></offset>
		<text class="richtext_FFFFFF">个碎片未清理</text>
	]]>
  </s>

  <s id="123">
    <![CDATA[
    <text class="richtext_333333_17">您的电脑有</text>
    <text class="text_EF7319_18">%s</text>
    <text class="richtext_333333_17">项问题待优化</text>
	]]>
  </s>

  <s id="126">
    <![CDATA[
   <text class="richtext_base_text3">%s存在</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>
		<offset length="2"></offset>
		<text class="richtext_base_text3">空间可释放</text>
	]]>
  </s>

  <s id="127">
    <![CDATA[
		<text class="richtext_FFFFFF">%s存在</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>
		<offset length="2"></offset>
		<text class="richtext_FFFFFF">空间可释放</text>
	]]>
  </s>

  <s id="128">
    <![CDATA[
		<text class="richtext_FFFFFF">存在</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>
		<offset length="2"></offset>
    <text class="richtext_FFFFFF">款</text>
		<text class="richtext_FF0000">问题软件</text>
	]]>
  </s>

  <s id="129">
    <![CDATA[
		<text class="richtext_666666_14">软件名称 : </text>
		<offset length="2"></offset>
    <text class="richtext_666666_14">%s</text>
	]]>
  </s>

  <s id="130">
    <![CDATA[
		<text class="richtext_base_text3">CPU温度</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>
        <text class="richtext_base_text3">，</text>
		<offset length="2"></offset>
		<text class="richtext_base_text3">请降温保护电脑</text>
	]]>
  </s>

  <s id="131">
    <![CDATA[
		<text class="richtext_FFFFFF">CPU温度</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>
        <text class="richtext_FFFFFF">，</text>
		<offset length="2"></offset>
		<text class="richtext_FFFFFF">请降温保护电脑</text>
	]]>
  </s>

  <s id="132">
    <![CDATA[
		<toast template="ToastGeneric" duration="long" launch="kismain:/vip:smart_cooling /panel:0 /from:1943" activationType="protocol">
	    <visual>
		    <binding template="ToastImageAndText02">
			    <image id="1" src="file:///%s"/>
			    <text id="1">%s</text>
			    <text id="2">温度长时间过高，可能会损坏电脑硬件</text>
		    </binding>
	    </visual>
	    <actions>
		    <action content="立即降温" activationType="protocol" arguments="kismain:/vip:smart_cooling /panel:0 /from:1943"/>
	    </actions>
	  </toast>
	]]>
  </s>

  <s id="133">
    <![CDATA[
		<toast template="ToastGeneric" duration="long" launch="kismain:/vip:smart_cooling /shutdown:10" activationType="protocol">
	    <visual>
		    <binding template="ToastImageAndText02">
			    <image id="1" src="file:///%s"/>
			    <text id="1">%s</text>
			    <text id="2">关机前请先保存程序数据避免丢失</text>
		    </binding>
	    </visual>
	    <actions>
		    <action content="立即关机" activationType="protocol" arguments="kismain:/vip:smart_cooling /shutdown:10"/>
	    </actions>
	  </toast>
	]]>
  </s>

  <s id="134">
    <![CDATA[
		<text class="richtext_base_text3">CPU温度</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>
        <text class="richtext_base_text3">，</text>
		<offset length="2"></offset>
		<text class="richtext_base_text3">建议关机保护电脑</text>
	]]>
  </s>

  <s id="135">温度长时间过高，可能会损坏电脑硬件</s>

  <s id="136">关机前请先保存程序数据避免丢失</s>

  <s id="137">
    <![CDATA[
		<text class="richtext_FFFFFF">CPU温度</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>
        <text class="richtext_FFFFFF">，</text>
		<offset length="2"></offset>
		<text class="richtext_FFFFFF">建议关机保护电脑</text>
	]]>
  </s>

  <s id="138">启用%s浏览器插件，拦截诈骗等风险网址，保护上网安全</s>

  <s id="139">
    <![CDATA[
			<toast template="ToastGeneric" duration="long" launch="kismain:/vip:kdiskopt_largeclean /autoscan /from:1 /vip_from:2101" activationType="protocol">
	    <visual>
		    <binding template="ToastImageAndText02">
			    <image id="1" src="file:///%s"/>
			    <text id="1">%s</text>
			    <text id="2">清理大文件，释放硬盘空间</text>
		    </binding>
	    </visual>
	    <actions>		    
         <action content="立即清理" activationType="protocol" arguments="kismain:/vip:kdiskopt_largeclean /autoscan /from:1 /vip_from:2101"/>
	    </actions>
	  </toast>
	]]>
  </s>

  <s id="140">
    <![CDATA[
		<text class="richtext_FFFFFF">硬盘大文件超过</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>        
		<offset length="2"></offset>		
	]]>
  </s>

  <s id="143">
    <![CDATA[
		<text class="richtext_FFFFFF">硬盘重复文件超过</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>        
		<offset length="2"></offset>		
	]]>
  </s>

  <s id="144">
    <![CDATA[
		<text class="richtext_FFFFFF">硬盘微信文件超过</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>        
		<offset length="2"></offset>		
	]]>
  </s>

  <s id="145">
    <![CDATA[
		<text class="richtext_FFFFFF">硬盘QQ文件超过</text>
		<offset length="2"></offset>
        <text class="richtext_number_FF0000_14">%s</text>        
		<offset length="2"></offset>		
	]]>
  </s>
  <!---重复文件单独使用--->
  <s id="141">
    <![CDATA[
			<toast template="ToastGeneric" duration="long" launch="kismain:/vip:kdupcleanup /autoscan /from:1 /vip_from:2151" activationType="protocol">
	    <visual>
		    <binding template="ToastImageAndText02">
			    <image id="1" src="file:///%s"/>
			    <text id="1">%s</text>
			    <text id="2">清理重复文件，释放硬盘空间</text>
		    </binding>
	    </visual>
	    <actions>		    
         <action content="立即清理" activationType="protocol" arguments="kismain:/vip:kdupcleanup /autoscan /from:1 /vip_from:2151"/>
	    </actions>
	  </toast>
	]]>
  </s>
  
  <!---微信和QQ清理公用--->
  <s id="142">
    <![CDATA[
			<toast template="ToastGeneric" duration="long" launch="%s" activationType="protocol">
         
	    <visual>
		    <binding template="ToastImageAndText02">
			    <image id="1" src="file:///%s"/>
			    <text id="1">%s</text>
			    <text id="2">清理冗余文件，释放硬盘空间</text>
		    </binding>
	    </visual>
	    <actions>		    
         <action content="立即清理" activationType="protocol" arguments="%s"/>
	    </actions>
	  </toast>
	]]>
  </s>

  <!---蓝屏错误码展示-->
  <s id="146">
    <![CDATA[
    <text class="bsod_error_code">错误码：</text>
    <break></break>
    <text class="bsod_error_code">%s</text>
	]]>
  </s>

  <s id="147">
    <![CDATA[
    <text class="bsod_error_code">文件：</text>
    <break></break>
    <text class="bsod_error_code">%s</text>
	]]>
  </s>
  
  <s id="148">
    <![CDATA[
    <text id="99" class="dump_rightmenu_middle_sub" tip="%s" notifymsg="1">异常模块：%s</text>
    <text class="dump_rightmenu_middle_sub">的右键菜单组件</text>
	]]>
  </s>
  <s id="149">
    <![CDATA[
    <text id="99" class="dump_rightmenu_right_sub" tip="%s" notifymsg="1">异常模块：%s</text>
    <text class="dump_rightmenu_right_sub">的右键菜单组件</text>
	]]>
  </s>
  
  <s id="150"><![CDATA[“%s”安装完成]]></s>
  <s id="151">
    <![CDATA[
    <text class="semauthority_subpage_title_black">残留安装包等垃圾文件需要清理，可释放</text>
    <offset length="4"></offset>
    <text class="semauthority_subpage_title_orange">%s</text>
    <offset length="4"></offset>
    <text class="semauthority_subpage_title_black">磁盘空间</text>
    ]]>
  </s>
  <s id="152">
    <![CDATA[
    <text class="diskslow_dark_main">发现</text>
		<offset length="-4"></offset>
    <text class="diskslow_dark_main_red">%d个</text>
    <offset length="-4"></offset>
    <text class="diskslow_dark_main">程序占用资源高，导致电脑卡慢</text>
    ]]>
  </s>
  <s id="153">
    <![CDATA[
    <text class="diskslow_light_main">发现</text>
		<offset length="-4"></offset>
    <text class="diskslow_light_main_red">%d个</text>
    <offset length="-4"></offset>
    <text class="diskslow_light_main">程序占用资源高，导致电脑卡慢</text>
    ]]>
  </s>
  <s id="154">
    <![CDATA[
    <text class="winoptimize_title" >发现</text>
    <text class="winoptimize_red" >%d个</text>
    <text class="winoptimize_title" >隐私权限被开启，存在</text>
    <text class="winoptimize_red" >信息泄漏风险</text>
    ]]>
  </s>
  <s id="155">
    <![CDATA[
    <text class="text_7E868E">碎片大小%s，碎片率%d%%</text>
    ]]>
  </s>
  <s id="156">
    <![CDATA[
    <text class="text_7E868E">影响磁盘运行速度，降低电脑使用寿命</text>
    ]]>
  </s>

	<s id="167">
		<![CDATA[
    <text class="recycle_pop_main_text_white">电脑仍有</text>
    <offset length="-3"/>
    <text class="recycle_pop_main_text_yellow">%.1fG</text>
    <offset length="-3"/>
    <text class="recycle_pop_main_text_white">垃圾！</text>
  ]]>
	</s>

	<s id="168">
		<![CDATA[
    <text class="recycle_pop_main_text_white">电脑仍有超</text>
    <offset length="-3"/>
    <text class="recycle_pop_main_text_yellow">100G</text>
    <offset length="-3"/>
    <text class="recycle_pop_main_text_white">垃圾！</text>
	  ]]>
	</s>
</string>