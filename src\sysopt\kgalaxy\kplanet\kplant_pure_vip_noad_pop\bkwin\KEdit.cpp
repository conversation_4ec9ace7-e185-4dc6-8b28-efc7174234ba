#include "stdafx.h"
#include "KEdit.h"

KEdit::KEdit()
{
	m_nullBrush = CreateSolidBrush(RGB(255, 255, 255));
	m_crText = RGB(255, 255, 255);
	m_hNotify = NULL;
}
KEdit::~KEdit()
{
	if (m_hWnd)
	{
		DestroyWindow();
	}

	if (m_nullBrush)
		DeleteObject(m_nullBrush);
}

void KEdit::OnKillFocus(CWindow wndFocus)
{
	if (m_hNotify)
		SendMessage(m_hNotify, WM_FLOATWND_EDIT_KILLFOCUS, (WPARAM)EN_KILLFOCUS, (LPARAM)m_hWnd);

	SetMsgHandled(FALSE);
}

void KEdit::OnGetFocus(CWindow wndLostFocus)
{
	if (m_hNotify)
		SendMessage(m_hNotify, WM_FLOATWND_EDIT_KILLFOCUS, (WPARAM)EN_SETFOCUS, (LPARAM)m_hWnd);

	SetMsgHandled(FALSE);
}

void KEdit::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags)
{
	if (nChar == VK_ESCAPE)
	{
		return;
	}
	SetMsgHandled(FALSE);
}

void KEdit::SetText(LPCTSTR lpszString)
{
	SetWindowText(lpszString);
}

void KEdit::GetText(CString &strTitle)
{
	GetWindowText(strTitle);
}

void KEdit::SetNotify(HWND hWnd)
{
	m_hNotify = hWnd;
}

void KEdit::SetColor(DWORD dwColor)
{
	m_crText = dwColor;
	//Invalidate(TRUE);
}

HBRUSH KEdit::OnCtlColor(CDCHandle dc, CEdit edit)
{
	dc.SetTextColor(m_crText);
	return m_nullBrush;
}
HBRUSH KEdit::OnCtlColorStatic(CDCHandle dc, CStatic wndStatic)
{
	dc.SetTextColor(m_crText);
	return m_nullBrush;
}

LRESULT KEdit::OnEditChanged(UINT uNotifyCode, int nID, CWindow wndCtl)
{
	if (m_hNotify)
		::SendMessage(m_hNotify, WM_FLOATWND_EDIT_CHANGE, (WPARAM)EN_CHANGE, (LPARAM)m_hWnd);

	return 0;
}