 #pragma once
#include "KPopBase.h"

class KAudioAbnormalPop
	: public CBkDialogViewEx
	, public IShadowPaintHook
    , public KPopBase
{
public:
	enum PopType
	{
		PT_AUDIO_SERVICE = 0,
		PT_AUDIO_ICON = 1,
	};

	typedef struct _POP_INFO
	{
		DWORD dwPopId;
		CString strPopName;
		CString strMainTitle;
		CString strProblemDiscription;
		CString strRecommend;
		CStringA strAIconSkin;
		int nReportPopId;
		CString strFixCmdLine;

		_POP_INFO(DWORD dwPopId, CString strPopName, CString strMainTitle, CString strProblemDiscription,
			CString strRecommend, CStringA strIconSkin,int nReportPopId,CString strFixCmdLine)
		{
			this->dwPopId               = dwPopId;
			this->strPopName            = strPopName;
			this->strMainTitle          = strMainTitle;
			this->strProblemDiscription = strProblemDiscription;
			this->strRecommend          = strRecommend;
			this->strAIconSkin          = strIconSkin;
			this->nReportPopId          = nReportPopId;
			this->strFixCmdLine          = strFixCmdLine;
		}
		_POP_INFO()
		{

		}
	} POP_INFO;

	typedef std::map<PopType, POP_INFO> MAP_POP_INFO;

	KAudioAbnormalPop(kplanet::KPopTask* pHost, KAudioAbnormalPop::PopType popType);
	~KAudioAbnormalPop(void);

#define UM_MSG_AFTERINIT   (WM_USER + 1)


	enum enumResID{
		ID_TEXT_NEVER_SHOW = 120,
		ID_BTN_CLOSE = 121,
		ID_BTN_OK = 122,
		ID_IMG_ICON = 123,
		ID_TEXT_MAIN_TITLE = 124,
		ID_TEXT_PROBLEM_DESCRIPTION = 125,
		ID_TEXT_RECOMMEND = 126,
	};

public:
	BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
		BK_NOTIFY_ID_COMMAND(ID_TEXT_NEVER_SHOW, OnBtnNeverShow)
		BK_NOTIFY_ID_COMMAND(ID_BTN_CLOSE, OnBtnClose)
		BK_NOTIFY_ID_COMMAND(ID_BTN_OK, OnBtnOK)
		BK_NOTIFY_MAP_END()

		BEGIN_MSG_MAP_EX(KAudioAbnormalPop)
		MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
		MSG_WM_INITDIALOG(OnInitDialog)
		MSG_WM_TIMER(OnTimer)
		MSG_WM_MOUSEMOVE(OnMouseMove)
		MESSAGE_HANDLER(UM_MSG_AFTERINIT, OnMessageAfterInit)
		CHAIN_MSG_MAP(CBkDialogViewEx)
		END_MSG_MAP()

protected: 
	//public KPopBase
	virtual BOOL Init(CString strJsonParam);
	virtual BOOL UnInit();
	virtual DWORD GetPopId();
	virtual CString GetPopName();
	virtual BOOL CanShow();   
	virtual BOOL Show();

	// public IShadowPaintHook
	virtual BOOL NotifyShadowPaint(HDC hDC, CRect& rct);


protected:
	LRESULT OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/);
	void OnTimer(UINT_PTR nIDEvent);
	LRESULT OnMessageAfterInit(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/);
	void OnMouseMove(UINT nFlags, CPoint point);

	void OnBtnClose();
	void OnBtnNeverShow();
	void OnBtnOK();

private:
	BOOL _initPopInfo();
	void OnEndDlgImpl(UINT uRetCode);
	void _Report(int action);
	BOOL _IsClickedNeverShow();
	void ResiteDlgCommonImpl(int nWidth, int nHeight);
	void _UpdateUI();
	void _GotoFix();
	void _AutoClose();


private:
	kplanet::KPopTask * m_pHost;
	PopType m_popType;
	POP_INFO m_popInfo;
	DWORD m_dwMouseMoveLastTick;
};
