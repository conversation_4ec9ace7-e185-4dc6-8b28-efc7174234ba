 #pragma once
#include "KPopBase.h"
#include "KComfirmPopDlg.h"

class KPrinterAbnormalPop 
    : public KPopBase
    , public IViewHandler
{
public:
    KPrinterAbnormalPop(kplanet::KPopTask* pHost);

    ~K<PERSON>rinterAbnormalPop(void);

protected: // public IShadowPaintHook, public KPopBase
    virtual BOOL Init(CString strJsonParam);
    virtual BOOL UnInit();
    virtual DWORD GetPopId();
    virtual CString GetPopName();
    virtual BOOL CanShow();   
    virtual BOOL Show();

public:
    virtual void OnOK();
    virtual void OnClose();
    virtual void OnNeverShow();
    virtual void OnAutoClose();

private:
    void _Report(int action);
    
private:
    kplanet::KPopTask * m_pHost;

    KComfirmPopDlg * m_pDlg;
    int m_nABTest;
};
