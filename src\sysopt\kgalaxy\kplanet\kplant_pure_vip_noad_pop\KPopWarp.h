#pragma once
#include "dlg/KCleanAdSuggestPopDlg.h"
#include "dlg/KMachineLimitPopDlg.h"
#include "dlg\KVipFreePackPopDlg.h"
#include "dlg/KOfflinePopDlg.h"
#include "dlg/KDevMoveoutOfflinePopDlg.h"
#include "dlg/KScreenRecordPop.h"
#include "dlg/KAutoSpeedUpPop.h"
#include "dlg/KDefragPopDlg.h"
#include "dlg/KSoftDeepCleanPopDlg.h"
#include "dlg/KCommonPopDlg.h"
#include "dlg/KDriverManagerPopDlg.h"
#include "dlg/KScreenCaptureTaskbarPopDlg.h"
#include "dlg/KExplorerPopDlg.h"
#include "dlg/KTemperatureMonitorTipDlg.h"
#include "dlg/KTemperatureMonitorTipWintoastPop.h"
#include "dlg/KTemperatureMonitorTipVipDlg.h"
#include "dlg/KDiskMgrPop.h"
#include "dlg/KDiskMgrPopWinToast.h"
#include "dlg/KBlueScreenPopDlg.h"

namespace kplanet
{
    class KPopWarp : public KPopTask
    {
    public:
        KAS_BEGIN_COM_MAP(KPopWarp)
            KAS_COM_INTERFACE_ENTRY(KPopTask)
        KAS_END_COM_MAP()

        KPopWarp(void);
        ~KPopWarp(void);

        virtual BOOL STDMETHODCALLTYPE Initialize();
        virtual BOOL STDMETHODCALLTYPE UnInitialize();
        virtual BOOL STDMETHODCALLTYPE CanShow();
        virtual void STDMETHODCALLTYPE Show();
        typedef std::vector<KPopBase*> PopList;

    protected:
        void _AddPop(KPopBase* pop);
        BOOL _ParseJsonParam(LPCTSTR strParam);

    private:
        PopList m_poplist;
    };
}