#ifndef __ISAFEAPIHELPER_H__
#define __ISAFEAPIHELPER_H__


class ISafeApiHelper
{
public:
    virtual ~ISafeApiHelper() {}
    virtual BOOL InitKsapi() = 0;
    virtual void UnInitKsapi() = 0;
    virtual BOOL IsInit() = 0;

    virtual LONG KRegCreateKeyEx(HKEY hKey, LPCTSTR lpSubKey, DWORD Reserved, LPTSTR lpClass,  DWORD dwOptions,  REGSAM samDesired,
        LPSECURITY_ATTRIBUTES lpSecurityAttributes,  PHKEY phkResult, LPDWORD lpdwDisposition) = 0;

    virtual LONG KRegOpenKeyEx(HKEY hKey, LPCTSTR lpSubKey, DWORD ulOptions, REGSAM samDesired, PHKEY phkResult) = 0;
    virtual LONG KRegSetValueEx(HKEY hKey, LPCTSTR lpValueName, DWORD Reserved, DWORD dwType, const BYTE* lpData, DWORD cbData) = 0;
    virtual LONG KRegCloseKey(HKEY hKey) = 0;
};

#endif