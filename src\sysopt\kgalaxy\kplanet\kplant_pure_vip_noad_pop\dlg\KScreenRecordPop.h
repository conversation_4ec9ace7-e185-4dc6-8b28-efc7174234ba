 #pragma once
#include "KPopBase.h"
#include "CBkMenu.h"

class KScreenRecordPop : public CBkDialogViewEx,
	public IShadowPaintHook, public KPopBase, public BKMenuCallBack
{
public:
    KScreenRecordPop(kplanet::KPopTask* pHost);
    ~KScreenRecordPop(void);

#define UM_MSG_AFTERINIT   (WM_USER + 1)

    enum enumResID{
        ID_CLOSE = 100,
		ID_MAIN_BUTTON = 101,
        ID_SETTING_BUTTON = 102,
        ID_NOT_NEED_LINK = 103,
    };

    enum {
        ID_CLICK_NULL = 0,
        ID_CLICK_OK = 1,
        ID_CLICK_NOT_NEED_LINK = 2,
        ID_CLICK_CLOSE = 3,
        ID_CLICK_MENU = 4,
        ID_CLICK_NEVER = 5,
        ID_CLICK_AUTO_CLOSE = 6,
    };

public:
    BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
        BK_NOTIFY_ID_COMMAND(ID_CLOSE, OnBtnClose)
		BK_NOTIFY_ID_COMMAND(ID_MAIN_BUTTON, OnBtnClick)
		BK_NOTIFY_ID_COMMAND(ID_SETTING_BUTTON, OnBtnSetting)
        BK_NOTIFY_ID_COMMAND(ID_NOT_NEED_LINK, OnBtnNotNeed)
        BK_NOTIFY_MAP_END()

        BEGIN_MSG_MAP_EX(KScreenRecordPop)
        MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
        MSG_WM_INITDIALOG(OnInitDialog)
        MESSAGE_HANDLER(UM_MSG_AFTERINIT, OnMessageAfterInit)
        MSG_WM_TIMER(OnTimer)
        CHAIN_MSG_MAP(CBkDialogViewEx)
        END_MSG_MAP()

public:
    virtual BOOL Init(CString strJsonParam);
    virtual BOOL UnInit();
    virtual DWORD GetPopId(){return screen_record_pop_id;}
    virtual CString GetPopName(){return screen_record_pop_name;}
    virtual BOOL CanShow();
    virtual BOOL Show();

    virtual BOOL NotifyShadowPaint(HDC hDC, CRect& rct);
	virtual void MenuItemClick(int menuId);
	LRESULT OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/);
	void OnTimer(UINT_PTR nIDEvent);
    void OnBtnClose();
    void OnBtnClick();
    void OnBtnNotNeed();
	void OnBtnSetting();
    LRESULT OnMessageAfterInit(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/);

    void SwitchOnScreenRecordReg();

protected:
    kplanet::KPopTask* m_pHost;
	CBKMenu m_settingMenu;
};
