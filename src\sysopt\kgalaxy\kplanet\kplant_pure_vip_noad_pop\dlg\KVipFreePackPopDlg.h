 #pragma once
#include "KPopBase.h"
#include "..\KReporter.h"

class KVipFreePackPopDlg : public CBkDialogViewEx,
	public IShadowPaintHook, public KPopBase
{
public:
    KVipFreePackPopDlg(kplanet::KPopTask* pHost);
    ~KVipFreePackPopDlg(void);

    enum enumResID{
        ID_CLOSE = 100,
        ID_MAIN_BUTTON = 101,
        TXT_EXPIRED = 200,
    };

public:
    BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
        BK_NOTIFY_ID_COMMAND(ID_CLOSE, OnBtnClose)
		BK_NOTIFY_ID_COMMAND(ID_MAIN_BUTTON, OnBtnClick)
        BK_NOTIFY_MAP_END()

        BEGIN_MSG_MAP_EX(KVipFreePackPopDlg)
        MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
        MSG_WM_INITDIALOG(OnInitDialog)
        CHAIN_MSG_MAP(CBkDialogViewEx)
        END_MSG_MAP()

public:
    virtual BOOL Init(CString strJsonParam);
    virtual BOOL UnInit();
    virtual DWORD GetPopId(){return vip_free_pack_pop_id;}
    virtual CString GetPopName(){return vip_free_pack_pop_name;}
    virtual BOOL CanShow();
    virtual BOOL Show();

    virtual BOOL NotifyShadowPaint(HDC hDC, CRect& rct);
	LRESULT OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/);
    void OnBtnClose();
    void OnBtnClick();


protected:
    kplanet::KPopTask* m_pHost;
};
