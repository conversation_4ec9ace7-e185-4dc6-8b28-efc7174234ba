#include "stdafx.h"
#include "KSafeApiHelper.h"

#define  USE_SAFE_R3_FUN
#define  DEFINE_USE_SAFER3_CODE
#include "include/ksapi/usesafer3.h"

BOOL g_bDubaKSafeApiInit = FALSE;

BOOL KSafeApiHelper::InitKsapi()
{
    if (!g_bDubaKSafeApiInit && InitUseSafeR3() == S_OK)
    {
        g_bDubaKSafeApiInit = TRUE;
    }

    return g_bDubaKSafeApiInit;
}

void KSafeApiHelper::UnInitKsapi()
{
    if (g_bDubaKSafeApiInit)
    {
        UninitUseSafeR3();
        g_bDubaKSafeApiInit = FALSE;
    }
}


BOOL KSafeApiHelper::IsInit()
{
    return g_bDubaKSafeApiInit;
}

LONG KSafeApiHelper::KRegCreateKeyEx(HKEY hKey, LPCTSTR lpSubKey, DWORD Reserved, LPTS<PERSON> lpClass,  DWORD dwOptions,  REGSAM samDesired,
                                     LPSECURITY_ATTRIBUTES lpSecurityAttributes,  PHKEY phkResult, LPDWORD lpdwDisposition)
{
    if (g_bDubaKSafeApiInit)
    {
        return SafeR3_RegCreateKeyEx(hKey, lpSubKey, Reserved, lpClass, dwOptions, samDesired, lpSecurityAttributes, phkResult, lpdwDisposition);
    }
    return ERROR_NOT_SUPPORTED;
}

LONG KSafeApiHelper::KRegOpenKeyEx(HKEY hKey, LPCTSTR lpSubKey, DWORD ulOptions, REGSAM samDesired, PHKEY phkResult)
{
    if (g_bDubaKSafeApiInit)
    {
        return SafeR3_RegOpenKeyEx(hKey, lpSubKey, ulOptions, samDesired, phkResult);
    }
    return ERROR_NOT_SUPPORTED;
}

LONG KSafeApiHelper::KRegSetValueEx(HKEY hKey, LPCTSTR lpValueName, DWORD Reserved, DWORD dwType, const BYTE* lpData, DWORD cbData)
{
    if (g_bDubaKSafeApiInit)
    {
        return SafeR3_RegSetValueEx(hKey, lpValueName, Reserved, dwType, lpData, cbData);
    }
    return ERROR_NOT_SUPPORTED;
}

LONG KSafeApiHelper::KRegCloseKey(HKEY hKey)
{
    if (g_bDubaKSafeApiInit)
    {
        return SafeR3_RegCloseKey(hKey);
    }
    return ERROR_NOT_SUPPORTED;
}
