#pragma once
#include<string>

#include "KPopBase.h"
#include "CBkMenu.h"
#include "cf_public/core/theme/KSystemThemeChangeMonitor.h"
#include "cf_public/component/win_toast/win_toast_interface.h"

#include "duba_include\easyipc_new\easyipcclientwrap.h"
#include "duba_include\easyipc_new\easyipceventwrap.h"

class KDiskMgrPopWinToast: public KPopBase
{
public:
	KDiskMgrPopWinToast(kplanet::KPopTask* pHost,const std::wstring& Parse);
	~KDiskMgrPopWinToast(void);
public:
	virtual BOOL Init(CString strJsonParam);
    virtual BOOL UnInit();
    virtual DWORD GetPopId() ;
	virtual CString GetPopName();
    virtual BOOL CanShow();
    virtual BOOL Show();

	BOOL SetPopCase(const std::wstring& Parse);
private:

	std::wstring _DiskMainUIGUID;
	std::wstring _DiskWeChatGUID;
	std::wstring _DiskQQGUID	;	
	std::wstring _DiskRepeatGUID;

	std::wstring _JsonPopName;

	int _PopCase ;



protected:
    kplanet::KPopTask* m_pHost;
	KEasyIpcClientWrap m_ipcClient; 

	CString m_strSize ;
};
