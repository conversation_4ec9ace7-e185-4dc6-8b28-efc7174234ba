 #pragma once
#include "KPopBase.h"
#include "CBkMenu.h"
#include "cf_public/core/theme/KSystemThemeChangeMonitor.h"

class KDefragPopDlg : public CBkDialogViewEx,
    public IShadowPaintHook, public KPopBase, public BKMenuCallBack, public cf::theme::ISystemThemeChangeCallback
{
public:
    KDefragPopDlg(kplanet::KPopTask* pHost);
    ~KDefragPopDlg(void);

#define UM_MSG_AFTERINIT   (WM_USER + 1)

    enum enumResID{
        ID_CLOSE = 100,
		ID_CLEAN_BUTTON = 101,
        ID_SETTING_BUTTON = 102,
        ID_TIP_TEXT = 103,
        ID_IMG_LOGO = 104,
        ID_TEXT_TITLE = 105,
        ID_SUB_TEXT = 106,
        ID_HEADER = 109,
        ID_BG = 110,
        ID_TIP_SUB_TEXT = 111,
    };

    enum {
        ID_DEFAULT_OPEN = 0,
        ID_CLICK_CLEAN = 1,
        ID_CLICK_CLOSE = 2,
        ID_CLICK_SETTING = 3,
        ID_CLICK_NEVER = 4,
        ID_CLICK_AUTO_CLOSE = 5,
    };

public:
    BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
        BK_NOTIFY_ID_COMMAND(ID_CLOSE, OnBtnClose)
        BK_NOTIFY_ID_COMMAND(ID_SETTING_BUTTON, OnBtnSetting)
        BK_NOTIFY_ID_COMMAND(ID_CLEAN_BUTTON, OnBtnClean)
        BK_NOTIFY_MAP_END()

        BEGIN_MSG_MAP_EX(KDefragPopDlg)
        MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
        MSG_WM_INITDIALOG(OnInitDialog)
        MESSAGE_HANDLER(UM_MSG_AFTERINIT, OnMessageAfterInit)
        MSG_WM_TIMER(OnTimer)
        CHAIN_MSG_MAP(CBkDialogViewEx)
        END_MSG_MAP()

public:
    virtual BOOL Init(CString strJsonParam);
    virtual BOOL UnInit();
    virtual DWORD GetPopId(){return defrag_pop_id;}
    virtual CString GetPopName(){return defrag_pop_name;}
    virtual BOOL CanShow();
    virtual BOOL Show();
    virtual void OnSystemThemeChanged(cf::theme::SYSTEM_THEME_STYLE style) { SwitchToStyle(style); }

    virtual BOOL NotifyShadowPaint(HDC hDC, CRect& rct);
	virtual void MenuItemClick(int menuId);
	LRESULT OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/);
	void OnTimer(UINT_PTR nIDEvent);
    void OnBtnClose();
    void OnBtnSetting();
    void OnBtnClean();
    LRESULT OnMessageAfterInit(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/);
    CString GetDlgTextByMemorySize(ULONGLONG ullSize);

    protected:
    UINT GetXml();
    void SwitchToStyle(cf::theme::SYSTEM_THEME_STYLE style);
    BOOL CanShowSubDataText(CString &strFragSize);

protected:
    kplanet::KPopTask* m_pHost;
    CBKMenu m_settingMenu;
    ULONGLONG m_ullSize;
    int m_nDlgWidth;
    BOOL m_bOldStyle;
    int m_nPopStyle;
    ULONGLONG m_ullPercent;
    ULONGLONG m_ullAvgSize;
    ULONGLONG m_ullAvgFileSize;
    ULONGLONG m_ullFragmentFileNUm;

};
