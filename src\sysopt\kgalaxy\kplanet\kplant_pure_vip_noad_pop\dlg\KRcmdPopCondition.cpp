#include "stdafx.h"
#include "KRcmdPopCondition.h"
#include "cf_public/core/register/kregister.h"
#include "cf_public/core/time/KTime.h"

#define DEF_EXPLORER_RECOMMEND_POP_SUBKEY  L"SOFTWARE\\Kingsoft\\kexplorermain\\explorer_recommend_pop"
#define DEF_KEY_NO_SHOW_ANYMORE L"no_show_anymore"
#define DEF_KEY_NO_SHOW_DAYS L"no_show_days"
#define DEF_KEY_LAST_CALL_TIME L"lastcalltime"
#define DEF_KEY_LAST_SHOW_TIME L"lastshowtime"

BOOL KRcmdPopCondition::IsNoShowAnymore()
{
    DWORD dwStatus = 0;
    if (GetPopRegisterValue(DEF_KEY_NO_SHOW_ANYMORE, dwStatus))
    {
        return dwStatus == 1 ? TRUE : FALSE;
    }

    return FALSE;
}

BOOL KRcmdPopCondition::SetNoShowAnymore()
{
    return SetPopRegisterValue( DEF_KEY_NO_SHOW_ANYMORE, 1);
}

DWORD KRcmdPopCondition::GetNoShowDays()
{
    DWORD dwDays = 0;
    GetPopRegisterValue(DEF_KEY_NO_SHOW_DAYS,dwDays);
    return dwDays;
}

BOOL KRcmdPopCondition::SetNoshowDays(DWORD dwDays)
{
    return SetPopRegisterValue(DEF_KEY_NO_SHOW_DAYS, dwDays);
}

INT64 KRcmdPopCondition::GetLastCallTime()
{
    INT64 nTime = 0;
    GetPopRegisterInt64Value(DEF_KEY_LAST_CALL_TIME,nTime);
    return nTime;
}

BOOL KRcmdPopCondition::SetLastCallTime()
{
    cf::time::KTime sysTime = cf::time::KTime::GetCurrentTime();
    __time64_t nTime = sysTime.GetTime64();
    return SetPopRegisterInt64Value(DEF_KEY_LAST_CALL_TIME, nTime);
}

INT64 KRcmdPopCondition::GetLastShowTime()
{
    INT64 nTime = 0;
    GetPopRegisterInt64Value(DEF_KEY_LAST_SHOW_TIME,nTime);
    return nTime;
}

BOOL KRcmdPopCondition::SetLastShowTime()
{
    cf::time::KTime sysTime = cf::time::KTime::GetCurrentTime();
    __time64_t nTime = sysTime.GetTime64();
    return SetPopRegisterInt64Value(DEF_KEY_LAST_SHOW_TIME, nTime);
}

BOOL KRcmdPopCondition::SetPopRegisterValue(LPCTSTR szValueName, DWORD dwValue)
{
    cf::reg::KRegister reg;
    if (!reg.OpenEx(HKEY_CURRENT_USER, DEF_EXPLORER_RECOMMEND_POP_SUBKEY, FALSE))
    {
        return FALSE;
    }

    if (!reg.Write(szValueName, dwValue))
    {
        return FALSE;
    }

    return TRUE;
}

BOOL KRcmdPopCondition::SetPopRegisterInt64Value(LPCTSTR szValueName, INT64 nValue)
{
    cf::reg::KRegister reg;
    if (!reg.OpenEx(HKEY_CURRENT_USER, DEF_EXPLORER_RECOMMEND_POP_SUBKEY, FALSE))
    {
        return FALSE;
    }

    if (!reg.WriteInt64(szValueName, nValue))
    {
        return FALSE;
    }

    return TRUE;
}

BOOL KRcmdPopCondition::GetPopRegisterValue(LPCTSTR szValueName, DWORD& dwValue)
{
    cf::reg::KRegister reg;
    if (!reg.OpenEx(HKEY_CURRENT_USER, DEF_EXPLORER_RECOMMEND_POP_SUBKEY))
    {
        return FALSE;
    }

    if (!reg.Read(szValueName, dwValue))
    {
        return FALSE;
    }

    return TRUE;
}

BOOL KRcmdPopCondition::GetPopRegisterInt64Value(LPCTSTR szValueName, INT64& nValue)
{
    cf::reg::KRegister reg;
    if (!reg.OpenEx(HKEY_CURRENT_USER, DEF_EXPLORER_RECOMMEND_POP_SUBKEY))
    {
        return FALSE;
    }

    if (!reg.ReadInt64(szValueName, nValue))
    {
        return FALSE;
    }

    return TRUE;
}
