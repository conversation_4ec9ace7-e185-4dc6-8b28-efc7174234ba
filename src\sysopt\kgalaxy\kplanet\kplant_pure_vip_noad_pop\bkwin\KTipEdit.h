#ifndef _KTIPEDIT_H_
#define _KTIPEDIT_H_

#include "KEdit.h"

#define WM_FLOATWND_EDIT_KILLFOCUS (WM_USER + 3)
#define WM_FLOATWND_EDIT_CHANGE (WM_USER + 4)
#define WM_FLOATWND_EDIT_LBUTTONDOWN (WM_USER + 5)
#define WM_FLOATWND_POSTSETFOCUS (WM_USER + 6)

class KTipEdit
	: public CWindowImpl<KTipEdit>
{
public:
	KTipEdit();
	
	~KTipEdit();

	void SetStyle(int nStyle);

	void SetColor(DWORD dwColor);

	void SetNotify(HWND hWnd);

	int GetText(CString& rString);

	void SetLimitText(int nMaxcChar);

	void SetSel(int nStart, int nEnd);

	void SetFocus();

	void SetText(CString &strString);

	void SetText(LPCTSTR lpszString);

	void SetTipColor(COLORREF crTip);

	void SetMyTip(CString strMyTip);

	void SetTip(LPCTSTR szTip);

	void SetMargin(int nXMargin, int nYMargin);

	void SetFont(HFONT hFont);

	int OnCreate(LPCREATESTRUCT lpCreateStruct);

	BOOL OnEraseBkgnd(CDCHandle dc);

	void OnSetFocus(CWindow wndOld);

	void OnSize(UINT nType, CSize size);
	
	void OnLButtonDown(UINT nFlags, CPoint point);

	LRESULT OnEditChanged(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL &bHandled);

	LRESULT OnDeactive(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL &bHandled);

	LRESULT OnPostSetFucos(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL &bHandled);

	void PostFocus();

	BEGIN_MSG_MAP_EX(KTipEdit)
		MSG_WM_CREATE(OnCreate)
		MSG_WM_SIZE(OnSize)
		//MSG_WM_SETFOCUS(OnSetFocus)
		MSG_WM_LBUTTONDOWN(OnLButtonDown)
		MESSAGE_HANDLER(WM_FLOATWND_POSTSETFOCUS, OnPostSetFucos)
		MESSAGE_HANDLER(WM_FLOATWND_EDIT_KILLFOCUS, OnDeactive)
		MSG_WM_ERASEBKGND(OnEraseBkgnd)
		MESSAGE_HANDLER(WM_FLOATWND_EDIT_CHANGE, OnEditChanged)
		REFLECT_NOTIFICATIONS_EX()
		END_MSG_MAP()
public:
	void DoActive();

	void DeActive();

private:
	BOOL m_bTiping;
	CString m_sTip;
	COLORREF m_cfTip;

	COLORREF m_crText;
	HBRUSH m_nullBrush;

	KEdit m_ebedEdit;
	CFont m_font;
	int m_nxMargin;
	int m_nyMargin;
	HWND m_hNotify;
	int m_nStyle;
	CString m_strMyTip;
};


#endif