 #pragma once
#include "KPopBase.h"
#include "CBkMenu.h"

class KSearchboxPop
	: public CBkDialogViewEx
	, public IShadowPaintHook
    , public KPopBase
    , public BKMenuCallBack
{
public:
	KSearchboxPop(kplanet::KPopTask* pHost);
	~KSearchboxPop(void);

#define UM_MSG_AFTERINIT   (WM_USER + 1)

    enum MENU_ID
    {
        MI_NOT_REMIND_AGAIN = 1,
    };

	enum enumResID{
		ID_BTN_MENU = 100,
		ID_BTN_CLOSE = 101,
		ID_BTN_OK = 103,
	};

public:
	BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
		BK_NOTIFY_ID_COMMAND(ID_BTN_MENU, OnBtnMenu)
		BK_NOTIFY_ID_COMMAND(ID_BTN_CLOSE, OnBtnClose)
		BK_NOTIFY_ID_COMMAND(ID_BTN_OK, OnBtnOK)
		BK_NOTIFY_MAP_END()

		BEGIN_MSG_MAP_EX(KSearchboxPop)
		MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
		MSG_WM_INITDIALOG(OnInitDialog)
		MESSAGE_HANDLER(UM_MSG_AFTERINIT, OnMessageAfterInit)
		CHAIN_MSG_MAP(CBkDialogViewEx)
		END_MSG_MAP()

protected: 
	//public KPopBase
	virtual BOOL Init(CString strJsonParam);
	virtual BOOL UnInit();
	virtual DWORD GetPopId();
	virtual CString GetPopName();
	virtual BOOL CanShow();   
	virtual BOOL Show();

	// public IShadowPaintHook
	virtual BOOL NotifyShadowPaint(HDC hDC, CRect& rct);
    virtual void MenuItemClick(int menuId);

protected:
	LRESULT OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/);
	LRESULT OnMessageAfterInit(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/);

	void OnBtnClose();
	void OnBtnNeverShow();
	void OnBtnOK();
    void OnBtnMenu();

private:
	void OnEndDlgImpl(UINT uRetCode);
	void _Report(int action);
	BOOL _IsClickedNeverShow();
    BOOL IsTaskbarBottom();
    void ResiteDlgCommonImpl(int nWidth, int nHeight);
	void _UpdateUI();
	void _GotoFix();
	void _AutoClose();
    RECT GetSerachboxRect();
    void MenuInit();
    void AddMenuItem(LPCWSTR lpszTxt, MENU_ID eId);

private:
	kplanet::KPopTask * m_pHost;
    CBKMenu m_settingMenu;
};
