#pragma once

enum eCloseReason
{
    click_unknown = 0,
    click_view,
    click_close,
};

enum eNoShowReason
{
    avoid_unknown = 0,
    avoid_loginuser,
    avoid_get_datarecover_file_verfailed,
    avoid_get_independent_phonerecover_file_verfailed,
    avoid_get_indtegration_phonerecover_file_verfailed,
    avoid_old_datarecover_file,
    avoid_old_independent_phonerecover_file,
    avoid_old_indtegration_phonerecover_file,
    avoid_pcmgr,
    avoid_digt_safe,
    avoid_digt_antivirus,
    avoid_huorong,
    avoid_poped,
};

enum eCleanAdSuggestAction
{
    cad_click_default = 1,
    cad_click_clean,
    cad_click_setting,
    cad_click_nomoreNotify,
	cad_click_close,
	cad_noclick_timeout,
};

enum eCleanAdSuggestNoShowReason
{
    reason_unknown = 0,
    reason_notvip,
    reason_nomoreNotify,
    reason_avoidByNdayShowCondition,
    reason_nosoftneedtobepurify,
};

enum POP_STYLE
{
    POP_STYLE_OLD = 1,
    POP_STYLE_NEW_DARK,
    POP_STYLE_NEW_LIGHT,
};

enum KLSOFTMGR_POP_TYPE
{
    KPT_DEFAULT = 0,
    KPT_NORMAL_POP = 1,
    KPT_RESIDUE_POP = 2,
    KPT_TOAST_POP = 3,
    KPT_ANTIREINSTALL = 4,
    KPT_ANTIREINSTALL_RET = 5,
};

struct CleanAdSuggestPopReportInfo
{
    int vip_version;
    CString openid;
    int show_from;
    bool is_show;
    int click_type;
    int noshow_reason;

    CleanAdSuggestPopReportInfo()
    : click_type(cad_click_default)
    , vip_version(0)
    , show_from(1)
    , is_show(false)
    , noshow_reason(reason_unknown)
    {}
};

enum KTEMPERATURE_POP_TYPE
{
    KPTEMP_DEFAULT = 0,
    KPTEMP_NORMAL_POP = 1,
    KPTEMP_NORMAL_HIGH_POP = 2,
    KPTEMP_VIP_POP = 5,
    KPTEMP_VIP_HIGH_POP = 6,
};

enum KTEMPERATURE_POP_ACTION
{
    KPA_DEFAULT = 0,
    KPA_CLEAN = 1,
    KPA_CLOSE = 2,
    KPA_NOT_TIP_CLICK = 3,
    KPA_SETTING = 4,
    KPA_CONNNECT_CS = 5,
    KPA_SHUTDOWN_PROCESS = 6,
    KPA_SHUTDOWN_MACHINE = 7,
    KPA_LOW_TEMP = 8,
};

enum KDISK_MANAGER_POP_TYPE
{
	KPDISK_DEFAULT = 0,
	KPDISK_NORMAL_POP = 1,	
	KPDISK_VIP_POP = 2,	
};

enum KDISK_MANAGER_POP_ACTION
{
	K_DISK_PA_DEFAULT = 0,
	K_DISK_PA_CLEAN = 1,
	K_DISK_PA_CLOSE = 2,
	K_DISK_PA_NOT_TIP_CLICK = 3,
	K_DISK_PA_SETTING = 4,
	K_DISK_PA_CONNNECT_CS = 5,
	K_DISK_PA_SHUTDOWN_PROCESS = 6,
	K_DISK_PA_SHUTDOWN_MACHINE = 7,
};

class KReporter
{
public:
    static void ReportPopResult(BOOL bShow, eCloseReason closeReason, eNoShowReason noShowReason);
    static void ReportCleanAdSuggestPopBehave(CleanAdSuggestPopReportInfo& info);
	static void ReportUsageReportPopShow(
		DWORD tips_click, DWORD no_show_click, DWORD pure_count, DWORD pay_type);
    static BOOL IsHitUnshowReportRateControl(DWORD dwRate);
    static void ReportMachineLimit(BYTE click, BYTE box_type);

    static void ReportNewvipPopShow(int nPopType, int nShow, LPCWSTR lpszFailreason, LPCWSTR lpszOpenid, int nExpireDay);
    static void ReportNewvipPopResult(int nPopType, int operation, LPCWSTR lpszOpenid, int nExpireDay);
    static void ReportOfflinePop(int nShow, int nClick, const std::wstring& strOpenId, const CString& strLocation, const CString& strAccount, const CString& strLastLoginTime);

    //
    static void ReportScreenRecordPop(BOOL isShow, DWORD pop_click, std::wstring processname = L"");
    static void ReportDefragPop( DWORD pop_click, int nPopStyle);
    static void ReportScreenCapture(DWORD dwClick);
    static void ReportSoftDeepCleanPop( DWORD dwSoftId, CString& strSoftname, DWORD dwClick, ULONGLONG dwSizeKB );
    static void ReportDriverManagerPop(DWORD dwAction, int nPopType = 1,int strategyid = 1);
    static void ReportFileDestroyPop( DWORD dwAction, DWORD dwSize);
    static void ReportFileDestroyPop( DWORD dwAction, DWORD dwSize, DWORD nABTestHit);
	static void ReportSysSlimCleanPop(int nSource,int nAction,ULONGLONG ullSysSize,ULONGLONG ullRemainSize,int nPopStyle,int nVersion,int nPopType,
									  int nProductId,DWORD dwCleanSize,int nOrigin);
    static void ReportKLSoftmgrPop(DWORD dwPopType, DWORD dwAction,const CString& strSoftName);
    static void ReportKTemperaturePop(DWORD dwPopType, DWORD dwAction, int nTemp);

	static void ReportKDiskMgrPop(DWORD dwPopType, DWORD dwAction,DWORD dwFroming);
    static void ReportKDoctorPop(int popid,int action,const CString& strSoftName,const int nNum);
    static void ReportDGRecycleScanTip(int dwAction, int nSence,ULONGLONG nSize);
};
