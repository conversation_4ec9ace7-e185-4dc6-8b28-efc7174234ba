
#ifndef __KBkSoftIconImage__h__
#define __KBkSoftIconImage__h__

#include "publish\systemopt\GdiplusImageWrap.h"
#include "publish\systemopt\KSoftIconMgr.h"
#include "publish\systemopt\KSoftIconMgrEx.h"

class KBkSoftIconImage : public CBkImageWnd
{
    BKOBJ_DECLARE_CLASS_NAME(KBkSoftIconImage, "softiconImge")

public:
    KBkSoftIconImage()
    {
        m_dwSoftId   = 0;
        m_dwSoftIdRes = 0;
        m_pImage     = NULL;
        m_bGray      = FALSE;
        m_nAlphaskin = 255;
        m_SizeImg.SetSize(48, 48);
    }

    ~KBkSoftIconImage()
    {
    }

protected:
    LRESULT OnNcCalcSize(BOOL bCalcValidRects, LPARAM lParam)
    {
        LPSIZE pSize = (LPSIZE)lParam;

        pSize->cx = ScaleByDpi(m_SizeImg.cx);
        pSize->cy = ScaleByDpi(m_SizeImg.cy);

        return 0;
    }

    static Gdiplus::Image *LoadImage(DWORD dwSoftId, DWORD dwSoftIdRes)
    {
        Gdiplus::Image *pImage = NULL;
        if (pImage == NULL)
        {
            pImage
                = KSoftIconMgrEx::Instance().GetSoftIcon(dwSoftId, dwSoftIdRes);
        }
        if (pImage == NULL)
        {
            pImage = KSoftIconMgrAuto::Instance().GetSoftIcon(dwSoftId,
                                                              dwSoftIdRes);
        }
        return pImage;
    }

    void OnPaint(CDCHandle dc)
    {
        if (m_dwSoftId == 0 
            && m_dwSoftIdRes == 0)
        {
            SetMsgHandled(FALSE);
            return;
        }

        if (m_pImage == NULL)
        {
            m_pImage = LoadImage(m_dwSoftId, m_dwSoftIdRes);
        }
        if (m_pImage == NULL)
            return;

        CBkBitmap *pBitmap = NULL;

        if (m_bGray)
        {
            DrawImgeToBmp(m_bmpGray, TRUE);
            pBitmap = &m_bmpGray;
        }
        else
        {
            DrawImgeToBmp(m_bmpNormal, FALSE);
            pBitmap = &m_bmpNormal;
        }

        if (pBitmap && pBitmap->m_hBitmap)
        {
            CBkBitmap::AlphaBlend(dc, m_rcWindow.left, m_rcWindow.top,
                                  m_rcWindow.Width(), m_rcWindow.Height(),
                                  *pBitmap, 0, 0, pBitmap->GetWidth(),
                                  pBitmap->GetHeight(), m_nAlphaskin);
        }
    }

public:
    BKWIN_DECLARE_ATTRIBUTES_BEGIN()
    BKWIN_CUSTOM_ATTRIBUTE("softid", OnSoftIDChange)
    BKWIN_INT_ATTRIBUTE("default_softid_res", m_dwSoftIdRes, 0)
    BKWIN_CUSTOM_ATTRIBUTE("size", OnSizeChange)
    BKWIN_INT_ATTRIBUTE("gray", m_bGray, TRUE)
    BKWIN_INT_ATTRIBUTE("alphaskin", m_nAlphaskin, TRUE)
    BKWIN_DECLARE_ATTRIBUTES_END()

    BKWIN_BEGIN_MSG_MAP()
    MSG_WM_PAINT(OnPaint)
    MSG_WM_NCCALCSIZE(OnNcCalcSize)
    BKWIN_END_MSG_MAP()

protected:
    HRESULT OnSoftIDChange(CStringA &strValue, BOOL bLoading)
    {
        DWORD dwSoftID = (DWORD)::atoi(strValue);
        if (m_dwSoftId != dwSoftID)
        {
            m_dwSoftId = dwSoftID;
            m_pImage   = NULL;
            m_bmpGray.DeleteObject();
            m_bmpNormal.DeleteObject();
        }

        return S_OK;
    }

    HRESULT OnSizeChange(CStringA &strValue, BOOL bLoading)
    {
        CSize sizeImg(0, 0);

        do
        {
            int nIndex = strValue.Find(L',', 0);
            if (nIndex == -1)
            {
                break;
            }

            CStringA strValueTmp = strValue.Mid(0, nIndex);
            sizeImg.cx           = atoi(strValueTmp);

            if (nIndex + 2 > strValue.GetLength())
            {
                break;
            }

            strValueTmp = strValue.Mid(nIndex + 1);
            sizeImg.cy  = atoi(strValueTmp);

            if (m_SizeImg != sizeImg)
            {
                m_SizeImg = sizeImg;

                if (!bLoading)
                    _RepositionSelf();
            }
        }
        while (FALSE);

        return S_OK;
    }

    void DrawImgeToBmp(CBkBitmap &bmpMem, BOOL bGray = FALSE)
    {
        if (bmpMem.m_hBitmap != NULL || m_pImage == NULL)
            return;

        CDC dcMem;
        dcMem.CreateCompatibleDC(NULL);

        CSize bmpSize;
        bmpSize.cx = ScaleByDpi(m_SizeImg.cx);
        bmpSize.cy = ScaleByDpi(m_SizeImg.cy);
        bmpMem.CreateDIBSection(bmpSize.cx, bmpSize.cy);

        HBITMAP hBmpOld = dcMem.SelectBitmap(bmpMem);

        CGdiplusImageWrap imageWrap(m_pImage);

        if (bGray)
            imageWrap.DrawImageGray(
                dcMem, CRect(0, 0, bmpSize.cx, bmpSize.cy),
                Gdiplus::InterpolationModeHighQualityBicubic);
        else
            imageWrap.DrawImage(dcMem, CRect(0, 0, bmpSize.cx, bmpSize.cy),
                                Gdiplus::InterpolationModeHighQualityBicubic);

        dcMem.SelectBitmap(hBmpOld);
        if (bGray)
        {
            bmpMem.MakeToGray();
        }
    }

protected:
    DWORD m_dwSoftId;
    DWORD m_dwSoftIdRes;
    BOOL  m_bGray;
    int   m_nAlphaskin;

    CBkBitmap m_bmpNormal;
    CBkBitmap m_bmpGray;
    CSize     m_SizeImg;

    Gdiplus::Image *m_pImage;

public:
    DWORD GetSoftID()
    {
        return m_dwSoftId;
    }
};

#endif // __KBkSoftIconImage__h__