#ifndef _KEDIT_H_
#define _KEDIT_H_

#define WM_FLOATWND_EDIT_KILLFOCUS (WM_USER + 3)
#define WM_FLOATWND_EDIT_CHANGE (WM_USER + 4)
#define WM_FLOATWND_EDIT_LBUTTONDOWN (WM_USER + 5)
#define WM_FLOATWND_POSTSETFOCUS (WM_USER + 6)

class KEdit : public CWindowImpl<KEdit, CEdit>
{
public:
	KEdit();
	~KEdit();

	void OnKillFocus(CWindow wndFocus);

	void OnGetFocus(CWindow wndLostFocus);

	void OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags);

	void SetText(LPCTSTR lpszString);

	void GetText(CString &strTitle);

	void SetNotify(HWND hWnd);

	void SetColor(DWORD dwColor);

	HBRUSH OnCtlColor(CDCHandle dc, CEdit edit);
	
	HBRUSH OnCtlColorStatic(CDCHandle dc, CStatic wndStatic);

	LRESULT OnEditChanged(UINT uNotifyCode, int nID, CWindow wndCtl);

	COLORREF m_crText;
	HBRUSH m_nullBrush;
	HWND m_hNotify;

	BEGIN_MSG_MAP_EX(KEdit)
		MSG_WM_KILLFOCUS(OnKillFocus)
		MSG_WM_SETFOCUS(OnGetFocus)
		MSG_WM_KEYDOWN(OnKeyDown)
		MSG_OCM_CTLCOLORSTATIC(OnCtlColorStatic)
		MSG_OCM_CTLCOLOREDIT(OnCtlColor)
		REFLECTED_COMMAND_CODE_HANDLER_EX(EN_CHANGE, OnEditChanged)
		REFLECT_NOTIFICATIONS_EX()
		END_MSG_MAP()
};

#endif	