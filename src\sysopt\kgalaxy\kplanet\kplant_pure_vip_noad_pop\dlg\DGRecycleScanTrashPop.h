#pragma once
#include "bkwin\CBkDialogViewImplEx.h"
#include "KDlgTimeoutBase.h"
#include "CBkMenu.h"
#include "KPopBase.h"
#include "cf_public\core\explorer\ExplorerInfoWrap.hpp"
#include "cf_public\core\singleinstance\KSingleInstance.hpp"
#include "include/framework/KSimpleIniWrap.h"

#define DG_WINDOW_CLASS_NAME L"CMainFrm"


#define WM_DG_RECYCLE_AFTERINIT (WM_USER + 200)
#define WM_DG_RECYCLE_ANIMATE   (WM_USER + 201)
#define WM_USRER_CLICK_MENU     (WM_USER + 100)
#define WM_USRER_AFTERINIT      (WM_USER + 200)



class DGRecycleScanTrashPop 
    : public CBkDialogViewEx
    , public KDlgTimeoutBase<DGRecycleScanTrashPop>
    , public IBkWindowPaintHook
    , public KPopBase
    , public BKMenuCallBack
{
public:
    enum SenceId 
    {
        EM_EmptyRecycle        = 1,
        EM_DeleteFile          = 2,
    };

    enum
    {
         WINDOW_WIDTH      = 229,
         WINDOW_MIN_HEIGHT = 90,
         WINDOW_MAX_HEIGHT = 130,
         SHOW_LEFT_OFFSET  = 0,
         SHOW_RIGH_OFFSET  = 0,
         GB_SIZE           = 1024*1024*1024,
         SCAN_DAY_INTERVAL = 1,
    };
    enum enumClickDef
    {
        enumShow         = 0,
        enumClickCleaner = 1,
        enumClickClose   = 2,
        enumClickNoPop   = 3,
        enumAutoTimeout  = 4,
    };

    enum
    {
        TIMER_ID_SCAN_ANIMATE            = 10,
        TIMER_ID_SCAN_ANIMATE_ELAPSE     = 30,
        TIMER_ID_SCAN_ANIMATE_TOTATLTIME = 1000,

        TIMER_ID_TIMER_OUT = 11,
    };

    enum
    {
        IDC_IMG_BACK = 200,

        IDC_DLG_POP_TOP_HEAD = 300,
        IDC_IMG_ICON         = 301,
        IDC_IMG_TEXT         = 302,
        IDC_CLOSE_BTN        = 303,
        IDC_BTN_MENU         = 304,
        IDC_RICH_TEXT        = 306,

        IDC_DLG_BOTTOM      = 400,

        IDC_RUN_CLEANER_BTN = 402,

    };

    enum EnumState
    {
        enumState_Normal   = 0,
        enumState_Scaning  = 1,
        enumState_ScanOver = 2,
    };

     enum EnumImgBgState
     {
        enumImgBgState_Left   = 1,
        enumImgBgState_Right  = 2,
        enumImgBgState_Top    = 4,
        enumImgBgState_Bottom = 8,
        enumImgBgState_Center = 16,
    };

    enum PopHideReasonCode
    {
        PopHideReasonCode_Unknow        = 0,
        PopHideReasonCode_ClickBtn      = 1,
        PopHideReasonCode_ClickClose    = 2,
        PopHideReasonCode_OutTime       = 3,
        PopHideReasonCode_CloseForever  = 4,
        PopHideReasonCode_FakePopUp     = 5,
        PopHideReasonCode_ClickCancel   = 6,
        PopHideReasonCode_ClickSetting  = 7,
        PopHideReasonCode_CloseRecently = 8,
    };

    struct AnimateData
    {
        float fCurrentTime;
        float fTotatleTime;

        AnimateData()
        {
            Clear();
        }

        void Clear()
        {
            ZeroMemory(this, sizeof(AnimateData));
        }
    };
public:
    DGRecycleScanTrashPop(kplanet::KPopTask *pHost);
    ~DGRecycleScanTrashPop(void);
    virtual void MenuItemClick(int menuId) override;

public:
    // BK消息映射
    BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
        BK_NOTIFY_ID_COMMAND(IDC_CLOSE_BTN, OnBtnClose)
        BK_NOTIFY_ID_COMMAND(IDC_RUN_CLEANER_BTN, OnBtnRunCleaner)
        BK_NOTIFY_ID_COMMAND(IDC_BTN_MENU, OnBtnShowMenu)
    BK_NOTIFY_MAP_END()

    BEGIN_MSG_MAP_EX(DGRecycleScanTrashPop)
        MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
        MSG_WM_DESTROY(OnDestroy)
        MESSAGE_HANDLER_EX(WM_USRER_CLICK_MENU, OnMsgClickMenu)
        MESSAGE_HANDLER_EX(WM_USRER_AFTERINIT, OnMsgAfterInit)
        CHAIN_MSG_MAP(CBkDialogViewEx)
        CHAIN_MSG_MAP(KDlgTimeoutBase<DGRecycleScanTrashPop>)
        MSG_WM_TIMER(OnTimer)
        MSG_WM_INITDIALOG(OnInitDialog)
    END_MSG_MAP()


protected:
    // 事件处理函数
    BOOL OnInitDialog(CWindow wndFocus, LPARAM lInitParam);
    void    OnTimer(UINT_PTR nIDEvent);
    void    OnDestroy();
    BOOL    InitWnd();

    //// 按钮事件处理
    void OnBtnClose();
    void OnBtnRunCleaner();
    void OnBtnNotShow();

    void OnBtnShowMenu();

    LRESULT OnMsgClickMenu(UINT uMsg, WPARAM wParam, LPARAM lParam);
    LRESULT OnMsgAfterInit(UINT uMsg, WPARAM wParam, LPARAM lParam);
    // 动画相关
    void StartScanningAnimate();
    void TimerForScanAnimate();
    void OnScanAnimateOver();

    //// 界面更新
    void UpdateView();

    //// 位置计算
    BOOL CalcWndShowRect(IN CSize sizeWnd, OUT CRect &rcOutWnd);
    BOOL GetRecylceIconRect();

    void CloseDlg();


    BOOL _SetRichText2(UINT uCtrlID, LPCSTR szRichText);


public:
    int OnTimeOut(DWORD dwTimerID);
    virtual BOOL NotifyPaint(CBkWindow *win, HDC hDC, const RECT &rcWin, BOOL bBeforePaint);
    void PaintImgProcessBack(CBkWindow &win, HDC hDC, const RECT &rcWin);

    virtual BOOL    Init(CString strJsonParam) override;
    virtual BOOL    UnInit() override;
    virtual DWORD   GetPopId()
    {
        return driver_geninus_recyclescan_pop_id;
    }
    virtual CString GetPopName()
    {
        return CF_STR_DG_RECYCLESCAN_POP_NAME;
    }  
    virtual BOOL    CanShow() override;
    virtual BOOL    Show() override;

private:
    ULONGLONG GetToalTrashScanSize();
    DWORD     GetTrashScanLastScanTime();
    BOOL      InitTrashConfig();
    BOOL      IsAutoCleanerSwitch();


private:
    kplanet::KPopTask *m_pHost;

    // 状态变量
    int         m_nState;
    AnimateData m_AnimateData;
    DWORD       m_dwImgBgStateMask;
    CRect       m_rcShortCutItem;
    CRect       m_rcWorkErea;

    // 配置参数
    CBKMenu m_PopMenu;
    DWORD   m_dwTimeout     = 60 * 1000;

    // 防重复显示
    cf::KSingleInstance m_hSingleInstance;
    static bool       m_isShowing;
    int       m_nDeleteSenceId   = SenceId::EM_DeleteFile;
    ULONGLONG m_ullTolSize = 0;
    KSimpleIniWrap      m_iniTrashConfigDate;
};