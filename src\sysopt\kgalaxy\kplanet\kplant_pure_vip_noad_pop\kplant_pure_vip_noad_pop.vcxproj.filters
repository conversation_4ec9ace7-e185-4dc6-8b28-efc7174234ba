﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
    <Filter Include="Resource Files\xml">
      <UniqueIdentifier>{327e2231-0568-42e8-8285-e2870c34418c}</UniqueIdentifier>
    </Filter>
    <Filter Include="import">
      <UniqueIdentifier>{cea11b19-485b-4cdf-8f40-0754d1f898e6}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\include">
      <UniqueIdentifier>{399d1928-e9ec-4b94-8c12-ad643fe1d6dd}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\include\framework">
      <UniqueIdentifier>{06c830de-680e-417b-ad0e-6ff8f51e37eb}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\include\miniutil">
      <UniqueIdentifier>{f2d69d2f-64b9-48e6-9acf-d0ff5293c676}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\include\productinfo">
      <UniqueIdentifier>{e02422c1-441c-49d6-ac47-bcfb69fa8b5a}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\include\publish">
      <UniqueIdentifier>{a35c82f9-f298-4e2c-821c-9e6bda0cac99}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\include\publish\json">
      <UniqueIdentifier>{8cbcf891-dd6d-4e01-9ada-1d8c8abf8d3a}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\include\popconfig">
      <UniqueIdentifier>{f33d8728-4a3e-458f-8399-cf6c2ffe45d0}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\include\Actor">
      <UniqueIdentifier>{8e62d711-75aa-49a3-9d0d-660eff213ab5}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\include\switch">
      <UniqueIdentifier>{89caf358-16ba-4300-8398-52b398dffca7}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\include\softpurify">
      <UniqueIdentifier>{658366f6-bdcc-4df4-b5f7-1ca4b63601d7}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\purevip">
      <UniqueIdentifier>{5d79b453-b7ce-4ef2-811e-72dcb68b2bff}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public">
      <UniqueIdentifier>{4502e2a4-23b0-4253-b984-3d3df5005d21}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\core">
      <UniqueIdentifier>{4aed4ff1-4647-4fe2-a784-0d2d6f90419e}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\core\string">
      <UniqueIdentifier>{cbc58516-6378-459e-9424-53ba8e4c84a9}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\core\json">
      <UniqueIdentifier>{f7619c84-6df2-4a7e-aa31-d2ca704ed03b}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\core\time">
      <UniqueIdentifier>{210c34d8-253f-4322-ba9a-45b7402cb028}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\core\ini">
      <UniqueIdentifier>{cc29efd9-4bb5-4123-bce6-ec087004a0eb}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\core\disk">
      <UniqueIdentifier>{297fa451-f7cc-43b8-905c-63fa21a020f7}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\core\win_version">
      <UniqueIdentifier>{3e106329-d6d8-4dbe-a25f-1dea7ca26a7d}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\core\path">
      <UniqueIdentifier>{30eb041b-fce7-4543-956a-4758175ed710}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\core\thread">
      <UniqueIdentifier>{40357023-cc16-4057-9093-40d388b7c328}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\core\define">
      <UniqueIdentifier>{d1baf55e-656b-47e2-aad0-25214f8fd8c2}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\core\singleton">
      <UniqueIdentifier>{2e888cd1-8bc5-4bb6-8e83-ba52adeaf66b}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\core\register">
      <UniqueIdentifier>{a3bfb148-f491-41fc-b12c-18ddecdf0de6}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\3rd">
      <UniqueIdentifier>{22705d95-90b4-4dec-b0a8-dc9eae7e9660}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\3rd\xml">
      <UniqueIdentifier>{32874543-6467-43d7-9f67-478291d24e80}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\component">
      <UniqueIdentifier>{729f7013-ec9a-4ed4-be76-aeb984fc1ade}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\component\magiccube">
      <UniqueIdentifier>{6bb2c819-813d-482c-9cc4-2a455f571b0d}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\cf_public\component\magiccube\include">
      <UniqueIdentifier>{2e496f6b-8b9a-4183-9907-be6b55049ead}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\popcenter">
      <UniqueIdentifier>{ef8e3191-90d1-4a1f-9e9e-d2ae170d39e8}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\publish">
      <UniqueIdentifier>{2eae17d6-cefc-4611-9ed5-8700544768f9}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\publish\systemopt">
      <UniqueIdentifier>{47903b61-ae0d-49cd-b26c-01194bcdae07}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\publish\NewsMinisite">
      <UniqueIdentifier>{683fa168-f6c9-4ad0-9a79-d2836f5bcf17}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\publish\NewsMinisite\urlcode">
      <UniqueIdentifier>{912400b8-bd81-4ac7-a8b9-3f1c6124bcf5}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\publish\kprocessbar">
      <UniqueIdentifier>{05eea567-42f8-48d3-b112-3d0e26a60f9b}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\duba_include">
      <UniqueIdentifier>{cec818fd-08dc-4eff-944d-b7384ba36e35}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\duba_include\framework">
      <UniqueIdentifier>{10771eb2-fc3f-4a5f-b4f9-a872e57018f1}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\src">
      <UniqueIdentifier>{a10fcaf3-4001-4ba6-b665-5791689b4535}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\src\sysopt">
      <UniqueIdentifier>{3b8f3205-b5b1-4133-bf22-8478923af62c}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\src\sysopt\publish">
      <UniqueIdentifier>{c96174ce-c124-4fa3-8998-ad4bf3d805b4}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\src\sysopt\publish\icondownloader">
      <UniqueIdentifier>{5f7bcf7d-3dea-4f41-bae1-2b85330fe7f2}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\src\sysopt\publish\ksoftmgr">
      <UniqueIdentifier>{d273df77-f253-4ee5-a173-8857ba3f000b}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\3rdparty">
      <UniqueIdentifier>{6f2a8e08-0506-4e96-b936-b22ac02fe13b}</UniqueIdentifier>
    </Filter>
    <Filter Include="import\3rdparty\zip">
      <UniqueIdentifier>{fdecf3a7-3a1d-4a80-bbfa-3747dc34f28a}</UniqueIdentifier>
    </Filter>
    <Filter Include="dlg">
      <UniqueIdentifier>{b42d77ca-2714-4a17-88b4-d2589e90a976}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public">
      <UniqueIdentifier>{44bd979c-168d-4972-97e7-f7c1fa347784}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public\core">
      <UniqueIdentifier>{99bdbec7-21fe-4fef-a882-2dd9d657a8bc}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public\core\log">
      <UniqueIdentifier>{def489ff-5862-4080-8720-b31d544486f1}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public\core\process">
      <UniqueIdentifier>{3baf232b-ebe6-4bea-8f3d-b6e14eab4a61}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public\core\screen">
      <UniqueIdentifier>{8b77f1f7-d561-40a3-b587-df66d9453d9b}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public\core\win_version">
      <UniqueIdentifier>{e1f460d7-2d74-4ec1-baac-eb9b5edb7776}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public\core\screenwatcher">
      <UniqueIdentifier>{bf2ed1f2-5c7c-4a40-8d33-4b411962ee6a}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public\core\url">
      <UniqueIdentifier>{553097ef-6256-4f55-9ebb-b37ae02961d8}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public\core\crc">
      <UniqueIdentifier>{27f36c2b-f68b-4e92-8d8c-054ccefe675c}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public\core\file">
      <UniqueIdentifier>{3fa41157-cbfc-4a25-baca-7740cfb0db19}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public\core\base64">
      <UniqueIdentifier>{634192ea-4e39-4d76-984d-44b2cb456382}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public\3rd">
      <UniqueIdentifier>{cc42fbc7-f164-4598-b5be-1b40e2f22786}</UniqueIdentifier>
    </Filter>
    <Filter Include="cf_public\3rd\xml">
      <UniqueIdentifier>{5eb96722-7316-45ad-94be-e50618900300}</UniqueIdentifier>
    </Filter>
    <Filter Include="bkwin">
      <UniqueIdentifier>{a5e35d81-8e18-4c30-82d6-0d9ba05ee92e}</UniqueIdentifier>
    </Filter>
    <Filter Include="SemAuthority">
      <UniqueIdentifier>{90581d6f-ed06-4b4f-966c-0c6e74876294}</UniqueIdentifier>
    </Filter>
    <Filter Include="duba_include">
      <UniqueIdentifier>{31233ae9-e13b-49c0-be29-8cec40043d1a}</UniqueIdentifier>
    </Filter>
    <Filter Include="duba_include\devmgr">
      <UniqueIdentifier>{2e39e5d3-0341-4739-a103-728d58f63c70}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <None Include="kplanet.def">
      <Filter>Source Files</Filter>
    </None>
    <None Include="bkwinres.rc2">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="kplant_pure_vip_noad_pop.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\systemopt\KPopConfig.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KRcmdPopCondition.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="KReporter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\systemopt\KTrashUtil.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="KUtil.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\framework\kanregisterkey.cpp">
      <Filter>import\include\framework</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\framework\KCreateXmlElementFunc.cpp">
      <Filter>import\include\framework</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\duba_include\framework\KDubaPath.cpp">
      <Filter>import\include\framework</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\framework\KIniWrapEx.cpp">
      <Filter>import\include\framework</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\framework\KSimpleIniWrap.cpp">
      <Filter>import\include\framework</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\framework\KSysService.cpp">
      <Filter>import\include\framework</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\framework\KTinyXml.cpp">
      <Filter>import\include\framework</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\productinfo\KNUMProductInfo.cpp">
      <Filter>import\include\productinfo</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\productinfo\KQQpcmProductInfo.cpp">
      <Filter>import\include\productinfo</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\3rdparty\jsoncpp\json_reader.cpp">
      <Filter>import\include\publish\json</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\3rdparty\jsoncpp\json_value.cpp">
      <Filter>import\include\publish\json</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\3rdparty\jsoncpp\json_writer.cpp">
      <Filter>import\include\publish\json</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\systemopt\KNaturalTime.cpp">
      <Filter>import\include\popconfig</Filter>
    </ClCompile>
    <ClCompile Include="KPopWarp.cpp">
      <Filter>import\include\popconfig</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\Actor\KActor.cpp">
      <Filter>import\include\Actor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\switch\cryptfunction.cpp">
      <Filter>import\include\switch</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\switch\kswitch.cpp">
      <Filter>import\include\switch</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\switch\kswitchwrap.cpp">
      <Filter>import\include\switch</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\softpurify\KSoftPurifyEngineWrapper.cpp">
      <Filter>import\include\softpurify</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\duba_include\purevip\KVipUsageRecord.cpp">
      <Filter>import\purevip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\string\string_number_conversions.cpp">
      <Filter>import\cf_public\core\string</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\string\string_split.cpp">
      <Filter>import\cf_public\core\string</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\string\string_util.cpp">
      <Filter>import\cf_public\core\string</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\string\sys_string_conversions_win.cpp">
      <Filter>import\cf_public\core\string</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\json\bkjson.cpp">
      <Filter>import\cf_public\core\json</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\time\KTime.cpp">
      <Filter>import\cf_public\core\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\ini\ini.cpp">
      <Filter>import\cf_public\core\ini</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\disk\DiskUtil.cpp">
      <Filter>import\cf_public\core\disk</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\win_version\KSystemVersion.cpp">
      <Filter>import\cf_public\core\win_version</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\thread\simple_thread.cpp">
      <Filter>import\cf_public\core\thread</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\define\def_time.cpp">
      <Filter>import\cf_public\core\define</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\singleton\singleton.cpp">
      <Filter>import\cf_public\core\singleton</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\register\kregister.cpp">
      <Filter>import\cf_public\core\register</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\3rd\xml\tinystr.cpp">
      <Filter>import\cf_public\3rd\xml</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\3rd\xml\tinyxml.cpp">
      <Filter>import\cf_public\3rd\xml</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\3rd\xml\tinyxmlerror.cpp">
      <Filter>import\cf_public\3rd\xml</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\3rd\xml\tinyxmlparser.cpp">
      <Filter>import\cf_public\3rd\xml</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\popcenter\KPopClient.cpp">
      <Filter>import\popcenter</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\common\KCommonTimeConfig.cpp">
      <Filter>import\publish</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\kinternetprotect\KInternetProtectFun.cpp">
      <Filter>import\publish</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\systemopt\GdiplusImageWrap.cpp">
      <Filter>import\publish\systemopt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\systemopt\KFunction.cpp">
      <Filter>import\publish\systemopt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\systemopt\KSoftIconMgr.cpp">
      <Filter>import\publish\systemopt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\systemopt\KSoftIconMgrEx.cpp">
      <Filter>import\publish\systemopt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\systemopt\localsoft_base.cpp">
      <Filter>import\publish\systemopt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\NewsMinisite\urlcode\URLEncode.cpp">
      <Filter>import\publish\NewsMinisite\urlcode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\kprogressbar\windowtool.cpp">
      <Filter>import\publish\kprocessbar</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\duba_include\kinternetprotect\KInternetProtectWrapper.cpp">
      <Filter>import\duba_include</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\duba_include\framework\kduba_product_info.cpp">
      <Filter>import\duba_include\framework</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\publish\icondownloader\KIconDownLoader.cpp">
      <Filter>import\src\sysopt\publish\icondownloader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\publish\icondownloader\KSoftIconUnzipper.cpp">
      <Filter>import\src\sysopt\publish\icondownloader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\publish\ksoftmgr\ksoftmgrenginewarp2.cpp">
      <Filter>import\src\sysopt\publish\ksoftmgr</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\3rdparty\zip\unzip.cpp">
      <Filter>import\3rdparty\zip</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KAudioAbnormalPop.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KAutoSpeedUpPop.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KBlueScreenPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KCleanAdSuggestPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KComfirmPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KCommonPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KDefragPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KDevMoveoutOfflinePopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KDiskMgrPop.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KDiskMgrPopWinToast.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KDiskslowPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KDriverManagerPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KDumpRightMenuPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KExplorerPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KInternetProtectPluginPop.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KMachineLimitPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KOfflinePopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KPrinterAbnormalPop.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KSatisfactionPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KScreenCaptureTaskbarPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KScreenRecordPop.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KSearchboxPop.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KSemAuthorityPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KSoftDeepCleanPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KSysOptimizeTaskBarPop.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KSystemTimeExceptionPop.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KTemperatureMonitorTipDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KTemperatureMonitorTipVipDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KTemperatureMonitorTipWintoastPop.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KVipFreePackPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\log\logging.cpp">
      <Filter>cf_public\core\log</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\process\KSysProcess.cpp">
      <Filter>cf_public\core\process</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\process\KSysProcessEx.cpp">
      <Filter>cf_public\core\process</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\screen\KScreenInfo.cpp">
      <Filter>cf_public\core\screen</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\screenWatcher\KScreenWatcher.cpp">
      <Filter>cf_public\core\screenwatcher</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\url\kurltools.cpp">
      <Filter>cf_public\core\url</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\crc\crc32.cpp">
      <Filter>cf_public\core\crc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\file\FileOperationUtil.cpp">
      <Filter>cf_public\core\file</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\base64\Base64.cpp">
      <Filter>cf_public\core\base64</Filter>
    </ClCompile>
    <ClCompile Include="bkwin\KEdit.cpp">
      <Filter>bkwin</Filter>
    </ClCompile>
    <ClCompile Include="bkwin\KTipEdit.cpp">
      <Filter>bkwin</Filter>
    </ClCompile>
    <ClCompile Include="SemAuthority\KSafeApiHelper.cpp">
      <Filter>SemAuthority</Filter>
    </ClCompile>
    <ClCompile Include="SemAuthority\KSemAuthorityPopMgr.cpp">
      <Filter>SemAuthority</Filter>
    </ClCompile>
    <ClCompile Include="SemAuthority\UserChoice_hash.cpp">
      <Filter>SemAuthority</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\duba_include\devmgr_engine\devmgr_engine_temp_wrapper.cpp">
      <Filter>duba_include\devmgr</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KInternetExceptionPop.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\KDGDriverManagerPopDlg.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="dlg\DGRecycleScanTrashPop.cpp">
      <Filter>dlg</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\duba123pop\KGetShortcutRect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\kdesk\independent_utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\kdesk\KDeskConfig.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\kis\src\kvip\include\framework\kscreenwatcher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="dlg\KRcmdPopCondition.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="KPopWarp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="KReporter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\systemopt\KTrashUtil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="KUtil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\kis\src\GUI\publish\bkres\bkres.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
    <ClInclude Include="bkwinres.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\include\framework\kanregisterkey.h">
      <Filter>import\include\framework</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\include\framework\ScreenUtil.h">
      <Filter>import\include\framework</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\include\3rdparty\jsoncpp\reader.h">
      <Filter>import\include\publish\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\include\Actor\KActor.h">
      <Filter>import\include\Actor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\include\switch\cryptfunction.h">
      <Filter>import\include\switch</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\include\switch\kswitch.h">
      <Filter>import\include\switch</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\include\switch\kswitchwrap.h">
      <Filter>import\include\switch</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\include\softpurify\KSoftPurifyEngineWrapper.h">
      <Filter>import\include\softpurify</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\duba_include\purevip\KVipUsageRecord.h">
      <Filter>import\purevip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\string\string_number_conversions.h">
      <Filter>import\cf_public\core\string</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\string\string_split.h">
      <Filter>import\cf_public\core\string</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\string\string_util.h">
      <Filter>import\cf_public\core\string</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\string\sys_string_conversions.h">
      <Filter>import\cf_public\core\string</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\json\bkjson.h">
      <Filter>import\cf_public\core\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\time\KTime.h">
      <Filter>import\cf_public\core\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\ini\ini.h">
      <Filter>import\cf_public\core\ini</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\disk\DiskUtil.h">
      <Filter>import\cf_public\core\disk</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\win_version\KSystemVersion.h">
      <Filter>import\cf_public\core\win_version</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\path\path.hpp">
      <Filter>import\cf_public\core\path</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\thread\KLocker.h">
      <Filter>import\cf_public\core\thread</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\thread\simple_thread.h">
      <Filter>import\cf_public\core\thread</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\define\def_time.h">
      <Filter>import\cf_public\core\define</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\singleton\singleton.h">
      <Filter>import\cf_public\core\singleton</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\register\kregister.h">
      <Filter>import\cf_public\core\register</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\3rd\xml\tinystr.h">
      <Filter>import\cf_public\3rd\xml</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\3rd\xml\tinyxml.h">
      <Filter>import\cf_public\3rd\xml</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\3rd\xml\tinyxml.hpp">
      <Filter>import\cf_public\3rd\xml</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\component\magiccube\include\KDubaMagicCubeWarpper.h">
      <Filter>import\cf_public\component\magiccube\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\popcenter\KPopClient.h">
      <Filter>import\popcenter</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\common\KCommonTimeConfig.h">
      <Filter>import\publish</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\kinternetprotect\KInternetProtectFun.h">
      <Filter>import\publish</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\systemopt\GdiplusImageWrap.h">
      <Filter>import\publish\systemopt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\systemopt\KFunction.h">
      <Filter>import\publish\systemopt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\systemopt\KSoftIconMgr.h">
      <Filter>import\publish\systemopt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\systemopt\KSoftIconMgrEx.h">
      <Filter>import\publish\systemopt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\systemopt\localsoft_base.h">
      <Filter>import\publish\systemopt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\NewsMinisite\urlcode\URLEncode.h">
      <Filter>import\publish\NewsMinisite\urlcode</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\kprogressbar\windowtool.h">
      <Filter>import\publish\kprocessbar</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\duba_include\kinternetprotect\KInternetProtectWrapper.h">
      <Filter>import\duba_include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\duba_include\framework\kduba_product_info.h">
      <Filter>import\duba_include\framework</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\publish\icondownloader\KIconDownLoader.h">
      <Filter>import\src\sysopt\publish\icondownloader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\publish\icondownloader\KSoftIconUnzipper.h">
      <Filter>import\src\sysopt\publish\icondownloader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\publish\ksoftmgr\ksoftmgrenginewarp2.h">
      <Filter>import\src\sysopt\publish\ksoftmgr</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\3rdparty\zip\unzip.h">
      <Filter>import\3rdparty\zip</Filter>
    </ClInclude>
    <ClInclude Include="dlg\CBkMenu.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KAudioAbnormalPop.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KAutoSpeedUpPop.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KBlueScreenPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KCleanAdSuggestPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KCleanTrashSettingMenu.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KComfirmPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KCommonPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KDefragPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KDevMoveoutOfflinePopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KDiskMgrPop.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KDiskMgrPopWinToast.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KDiskslowPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KDriverManagerPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KDumpRightMenuPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KExplorerPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KInternetProtectPluginPop.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KMachineLimitPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KOfflinePopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KPopBase.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KPrinterAbnormalPop.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KSatisfactionPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KScreenCaptureTaskbarPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KScreenRecordPop.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KSearchboxPop.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KSemAuthorityPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KSoftDeepCleanPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KSysOptimizeTaskBarPop.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KSystemTimeExceptionPop.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KTemperatureMonitorTipDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KTemperatureMonitorTipVipDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KTemperatureMonitorTipWintoastPop.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KVipFreePackPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\log\logging.h">
      <Filter>cf_public\core\log</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\process\KSysProcess.h">
      <Filter>cf_public\core\process</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\process\KSysProcessEx.h">
      <Filter>cf_public\core\process</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\screen\KScreenInfo.h">
      <Filter>cf_public\core\screen</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\screenWatcher\KScreenWatcher.h">
      <Filter>cf_public\core\screenwatcher</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\url\kurltools.h">
      <Filter>cf_public\core\url</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\crc\crc32.h">
      <Filter>cf_public\core\crc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\file\FileOperationUtil.h">
      <Filter>cf_public\core\file</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\cf_public\core\base64\Base64.h">
      <Filter>cf_public\core\base64</Filter>
    </ClInclude>
    <ClInclude Include="bkwin\KEdit.h">
      <Filter>bkwin</Filter>
    </ClInclude>
    <ClInclude Include="bkwin\KTipEdit.h">
      <Filter>bkwin</Filter>
    </ClInclude>
    <ClInclude Include="SemAuthority\KSafeApiHelper.h">
      <Filter>SemAuthority</Filter>
    </ClInclude>
    <ClInclude Include="SemAuthority\KSemAuthorityPopMgr.h">
      <Filter>SemAuthority</Filter>
    </ClInclude>
    <ClInclude Include="SemAuthority\UserChoice_hash.h">
      <Filter>SemAuthority</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\duba_include\devmgr_engine\devmgr_engine_temp_wrapper.h">
      <Filter>duba_include\devmgr</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KInternetExceptionPop.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KDGDriverManagerPopDlg.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="dlg\DGRecycleScanTrashPop.h">
      <Filter>dlg</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\duba123pop\KGetShortcutRect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\kdesk\independent_utils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\publish\kdesk\KDeskConfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="dlg\KDlgTimeoutBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\kis\src\kvip\include\framework\kscreenwatcher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\..\..\..\..\kis\src\GUI\publish\bkres\bkres.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
    <ResourceCompile Include="kplant_pure_vip_noad_pop.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Xml Include="res\assoftmgrpop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\clean_ad_suggest_dlg.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\def_skin.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\def_string.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\def_style.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\bluescreen\dlg_bluescreen.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\dlg_cleantrash_setting.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\dlg_setting_menu.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\dlg_sysslim_clean_pop2.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\dlg_sysslim_clean_pop2_new.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\dlg_sysslim_clean_pop3.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\audioabnormal\KAudioAbnormalPluginPop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\kautospeeduppop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\kcommonpop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\kdefragpop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\kdefragpop_new.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\KDiskManager.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\kdrivermanagerpop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\kdrivermanagerpop_new.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\kexplorerpop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\kfiledestroypop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\KInternetProtectPluginPop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\klsoftmgr_antireinstall_pop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\klsoftmgr_antireinstall_ret_pop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\klsoftmgr_pop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\kprcycleanerpop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\printerabnormal\KPrinterAbnormalPluginPop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\kscreencapturetaskbarpop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\ksoftdeepcleanpop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\KSystemTimeExceptionPop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\KTemperatureMonitorHightTipPop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\KTemperatureMonitorTipPop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\KTemperatureMonitorTipVipPop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\winoptimizetaskbar\KWinOptimizeTaskBarPop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\machine_limit_dlg.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\planet_vip_noad_dlg.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\diskslow\dlg_diskslow.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\internetexception\KInternetExceptionPop.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\satisfaction\dlg_satisfaction.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\kdrivermanagerpop_dg_new.xml">
      <Filter>Resource Files\xml</Filter>
    </Xml>
    <Xml Include="res\dlg_dg_scan_recycle.xml" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
</Project>