<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_mt|Win32">
      <Configuration>Release_mt</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectName>kplanet_pure_vip_noad_pop</ProjectName>
    <ProjectGuid>{B6C04A52-3265-4FBF-A215-0F43B3891FC6}</ProjectGuid>
    <RootNamespace>kplanetDemo</RootNamespace>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>16.0.33801.447</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>C:\Program Files (x86)\Mydrivers\DriverGenius2013\kplanet</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
    <GenerateManifest>false</GenerateManifest>
    <TargetName>kvipnoadpop</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'">
    <OutDir>..\..\..\..\..\product\win32\kplanet\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
    <GenerateManifest>true</GenerateManifest>
    <TargetName>kvipnoadpop</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;KPLANETDEMO_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>../../../../../cf_3rdparty/atlserver;../../../../../cf_3rdparty/wtl_10;../../../../../cf_public;./;../../../../../;../../../../../kis/src/GUI/publish;../../../../../include;../../../../../publish;../../../../../duba_include;../../../../../publish/3rd;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>__SKIPTHEME__;WIN32;NDEBUG;_WINDOWS;_USRDLL;KPLANETDEMO_EXPORTS;KPLANETDEMO;TIXML_USE_STL;USED_KDESKIPC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_UNICODE;UNICODE;KPLANET_PURE_VIP_UPDATE_POP;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>../../../../../include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Link>
      <AdditionalLibraryDirectories>..\..\..\..\..\lib\win32;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ModuleDefinitionFile>kplanet.def</ModuleDefinitionFile>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)dbginfo\$(TargetName).pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <PostBuildEvent>
      <Command>pecopy.exe -in "$(TargetPath)" -out "$(TargetDir)\drivergenius\$(TargetName)$(TargetExt)" -mpn "DriverGenius" -mfd "DriverGenius - $(TargetName)$(TargetExt)"</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>./;../../../../../;../../../../../kis/src/GUI/publish;../../../../../include;E:\Microsoft SDKs\Windows\v6.1\Include;../../../../../publish;../../../../../duba_include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;KPLANETDEMO_EXPORTS;KPLANETDEMO;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <AdditionalLibraryDirectories>..\..\..\..\..\lib\win32;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ModuleDefinitionFile>kplanet.def</ModuleDefinitionFile>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)dbginfo\$(TargetName).pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="bkwinres.rc2" />
    <None Include="kplanet.def" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\..\3rdparty\zip\unzip.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\3rd\xml\tinystr.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\3rd\xml\tinyxml.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\3rd\xml\tinyxmlerror.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\3rd\xml\tinyxmlparser.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\base64\Base64.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\crc\crc32.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\define\def_time.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\disk\DiskUtil.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\file\FileOperationUtil.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\ini\ini.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\json\bkjson.cpp">
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(IntDir)%(Filename)1.obj</ObjectFileName>
      <XMLDocumentationFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(IntDir)%(Filename)1.xdc</XMLDocumentationFileName>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'">$(IntDir)%(Filename)1.obj</ObjectFileName>
      <XMLDocumentationFileName Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'">$(IntDir)%(Filename)1.xdc</XMLDocumentationFileName>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(IntDir)%(Filename)1.obj</ObjectFileName>
      <XMLDocumentationFileName Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(IntDir)%(Filename)1.xdc</XMLDocumentationFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\log\logging.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\process\KSysProcess.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\process\KSysProcessEx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\register\kregister.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\screenWatcher\KScreenWatcher.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\screen\KScreenInfo.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\singleton\singleton.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\string\string_number_conversions.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\string\string_split.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\string\string_util.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\string\sys_string_conversions_win.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\thread\simple_thread.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\time\KTime.cpp">
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(IntDir)%(Filename)1.obj</ObjectFileName>
      <XMLDocumentationFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(IntDir)%(Filename)1.xdc</XMLDocumentationFileName>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'">$(IntDir)%(Filename)1.obj</ObjectFileName>
      <XMLDocumentationFileName Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'">$(IntDir)%(Filename)1.xdc</XMLDocumentationFileName>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(IntDir)%(Filename)1.obj</ObjectFileName>
      <XMLDocumentationFileName Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(IntDir)%(Filename)1.xdc</XMLDocumentationFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\url\kurltools.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\cf_public\core\win_version\KSystemVersion.cpp">
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(IntDir)%(Filename)1.obj</ObjectFileName>
      <XMLDocumentationFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(IntDir)%(Filename)1.xdc</XMLDocumentationFileName>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'">$(IntDir)%(Filename)1.obj</ObjectFileName>
      <XMLDocumentationFileName Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'">$(IntDir)%(Filename)1.xdc</XMLDocumentationFileName>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(IntDir)%(Filename)1.obj</ObjectFileName>
      <XMLDocumentationFileName Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(IntDir)%(Filename)1.xdc</XMLDocumentationFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\duba_include\devmgr_engine\devmgr_engine_temp_wrapper.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\duba_include\framework\KDubaPath.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\duba_include\framework\kduba_product_info.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\duba_include\kinternetprotect\KInternetProtectWrapper.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\duba_include\purevip\KVipUsageRecord.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\3rdparty\jsoncpp\json_reader.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\3rdparty\jsoncpp\json_value.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\3rdparty\jsoncpp\json_writer.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\Actor\KActor.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\framework\kanregisterkey.cpp" />
    <ClCompile Include="..\..\..\..\..\include\framework\KCreateXmlElementFunc.cpp" />
    <ClCompile Include="..\..\..\..\..\include\framework\KIniWrapEx.cpp" />
    <ClCompile Include="..\..\..\..\..\include\framework\KSimpleIniWrap.cpp" />
    <ClCompile Include="..\..\..\..\..\include\framework\KSysService.cpp" />
    <ClCompile Include="..\..\..\..\..\include\framework\KTinyXml.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\productinfo\KNUMProductInfo.cpp" />
    <ClCompile Include="..\..\..\..\..\include\productinfo\KQQpcmProductInfo.cpp" />
    <ClCompile Include="..\..\..\..\..\include\softpurify\KSoftPurifyEngineWrapper.cpp" />
    <ClCompile Include="..\..\..\..\..\include\switch\cryptfunction.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\switch\kswitch.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\include\switch\kswitchwrap.cpp" />
    <ClCompile Include="..\..\..\..\..\kis\src\kvip\include\framework\kscreenwatcher.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\common\KCommonTimeConfig.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\publish\duba123pop\KGetShortcutRect.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\kdesk\independent_utils.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\kdesk\KDeskConfig.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\kinternetprotect\KInternetProtectFun.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\kprogressbar\windowtool.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\NewsMinisite\urlcode\URLEncode.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\popcenter\KPopClient.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\systemopt\GdiplusImageWrap.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\systemopt\KFunction.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\systemopt\KNaturalTime.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\systemopt\KPopConfig.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\systemopt\KSoftIconMgr.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\systemopt\KSoftIconMgrEx.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\systemopt\KTrashUtil.cpp" />
    <ClCompile Include="..\..\..\..\..\publish\systemopt\localsoft_base.cpp" />
    <ClCompile Include="..\..\..\publish\icondownloader\KIconDownLoader.cpp" />
    <ClCompile Include="..\..\..\publish\icondownloader\KSoftIconUnzipper.cpp" />
    <ClCompile Include="..\..\..\publish\ksoftmgr\ksoftmgrenginewarp2.cpp" />
    <ClCompile Include="bkwin\KEdit.cpp" />
    <ClCompile Include="bkwin\KTipEdit.cpp" />
    <ClCompile Include="dlg\DGRecycleScanTrashPop.cpp" />
    <ClCompile Include="dlg\KAudioAbnormalPop.cpp" />
    <ClCompile Include="dlg\KAutoSpeedUpPop.cpp" />
    <ClCompile Include="dlg\KBlueScreenPopDlg.cpp" />
    <ClCompile Include="dlg\KCleanAdSuggestPopDlg.cpp" />
    <ClCompile Include="dlg\KComfirmPopDlg.cpp" />
    <ClCompile Include="dlg\KCommonPopDlg.cpp" />
    <ClCompile Include="dlg\KDefragPopDlg.cpp" />
    <ClCompile Include="dlg\KDevMoveoutOfflinePopDlg.cpp" />
    <ClCompile Include="dlg\KDGDriverManagerPopDlg.cpp" />
    <ClCompile Include="dlg\KDiskMgrPop.cpp" />
    <ClCompile Include="dlg\KDiskMgrPopWinToast.cpp" />
    <ClCompile Include="dlg\KDiskslowPopDlg.cpp" />
    <ClCompile Include="dlg\KDriverManagerPopDlg.cpp" />
    <ClCompile Include="dlg\KDumpRightMenuPopDlg.cpp" />
    <ClCompile Include="dlg\KExplorerPopDlg.cpp" />
    <ClCompile Include="dlg\KInternetExceptionPop.cpp" />
    <ClCompile Include="dlg\KInternetProtectPluginPop.cpp" />
    <ClCompile Include="dlg\KMachineLimitPopDlg.cpp" />
    <ClCompile Include="dlg\KOfflinePopDlg.cpp" />
    <ClCompile Include="dlg\KPrinterAbnormalPop.cpp" />
    <ClCompile Include="dlg\KRcmdPopCondition.cpp" />
    <ClCompile Include="dlg\KSatisfactionPopDlg.cpp" />
    <ClCompile Include="dlg\KScreenCaptureTaskbarPopDlg.cpp" />
    <ClCompile Include="dlg\KScreenRecordPop.cpp" />
    <ClCompile Include="dlg\KSearchboxPop.cpp" />
    <ClCompile Include="dlg\KSemAuthorityPopDlg.cpp" />
    <ClCompile Include="dlg\KSoftDeepCleanPopDlg.cpp" />
    <ClCompile Include="dlg\KSysOptimizeTaskBarPop.cpp" />
    <ClCompile Include="dlg\KSystemTimeExceptionPop.cpp" />
    <ClCompile Include="dlg\KTemperatureMonitorTipDlg.cpp" />
    <ClCompile Include="dlg\KTemperatureMonitorTipVipDlg.cpp" />
    <ClCompile Include="dlg\KTemperatureMonitorTipWintoastPop.cpp" />
    <ClCompile Include="dlg\KVipFreePackPopDlg.cpp" />
    <ClCompile Include="kplant_pure_vip_noad_pop.cpp" />
    <ClCompile Include="KPopWarp.cpp" />
    <ClCompile Include="KReporter.cpp" />
    <ClCompile Include="KUtil.cpp" />
    <ClCompile Include="SemAuthority\KSafeApiHelper.cpp" />
    <ClCompile Include="SemAuthority\KSemAuthorityPopMgr.cpp" />
    <ClCompile Include="SemAuthority\UserChoice_hash.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_mt|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\..\3rdparty\zip\unzip.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\3rd\xml\tinystr.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\3rd\xml\tinyxml.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\3rd\xml\tinyxml.hpp" />
    <ClInclude Include="..\..\..\..\..\cf_public\component\magiccube\include\KDubaMagicCubeWarpper.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\base64\Base64.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\crc\crc32.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\define\def_time.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\disk\DiskUtil.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\file\FileOperationUtil.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\ini\ini.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\json\bkjson.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\log\logging.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\path\path.hpp" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\process\KSysProcess.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\process\KSysProcessEx.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\register\kregister.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\screenWatcher\KScreenWatcher.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\screen\KScreenInfo.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\singleton\singleton.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\string\string_number_conversions.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\string\string_split.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\string\string_util.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\string\sys_string_conversions.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\thread\KLocker.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\thread\simple_thread.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\time\KTime.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\url\kurltools.h" />
    <ClInclude Include="..\..\..\..\..\cf_public\core\win_version\KSystemVersion.h" />
    <ClInclude Include="..\..\..\..\..\duba_include\devmgr_engine\devmgr_engine_temp_wrapper.h" />
    <ClInclude Include="..\..\..\..\..\duba_include\framework\kduba_product_info.h" />
    <ClInclude Include="..\..\..\..\..\duba_include\kinternetprotect\KInternetProtectWrapper.h" />
    <ClInclude Include="..\..\..\..\..\duba_include\purevip\KVipUsageRecord.h" />
    <ClInclude Include="..\..\..\..\..\include\3rdparty\jsoncpp\reader.h" />
    <ClInclude Include="..\..\..\..\..\include\Actor\KActor.h" />
    <ClInclude Include="..\..\..\..\..\include\framework\kanregisterkey.h" />
    <ClInclude Include="..\..\..\..\..\include\framework\ScreenUtil.h" />
    <ClInclude Include="..\..\..\..\..\include\softpurify\KSoftPurifyEngineWrapper.h" />
    <ClInclude Include="..\..\..\..\..\include\switch\cryptfunction.h" />
    <ClInclude Include="..\..\..\..\..\include\switch\kswitch.h" />
    <ClInclude Include="..\..\..\..\..\include\switch\kswitchwrap.h" />
    <ClInclude Include="..\..\..\..\..\kis\src\GUI\publish\bkres\bkres.h" />
    <ClInclude Include="..\..\..\..\..\kis\src\kvip\include\framework\kscreenwatcher.h" />
    <ClInclude Include="..\..\..\..\..\publish\common\KCommonTimeConfig.h" />
    <ClInclude Include="..\..\..\..\..\publish\duba123pop\KGetShortcutRect.h" />
    <ClInclude Include="..\..\..\..\..\publish\kdesk\independent_utils.h" />
    <ClInclude Include="..\..\..\..\..\publish\kdesk\KDeskConfig.h" />
    <ClInclude Include="..\..\..\..\..\publish\kinternetprotect\KInternetProtectFun.h" />
    <ClInclude Include="..\..\..\..\..\publish\kprogressbar\windowtool.h" />
    <ClInclude Include="..\..\..\..\..\publish\NewsMinisite\urlcode\URLEncode.h" />
    <ClInclude Include="..\..\..\..\..\publish\popcenter\KPopClient.h" />
    <ClInclude Include="..\..\..\..\..\publish\systemopt\GdiplusImageWrap.h" />
    <ClInclude Include="..\..\..\..\..\publish\systemopt\KFunction.h" />
    <ClInclude Include="..\..\..\..\..\publish\systemopt\KSoftIconMgr.h" />
    <ClInclude Include="..\..\..\..\..\publish\systemopt\KSoftIconMgrEx.h" />
    <ClInclude Include="..\..\..\..\..\publish\systemopt\KTrashUtil.h" />
    <ClInclude Include="..\..\..\..\..\publish\systemopt\localsoft_base.h" />
    <ClInclude Include="..\..\..\publish\icondownloader\KIconDownLoader.h" />
    <ClInclude Include="..\..\..\publish\icondownloader\KSoftIconUnzipper.h" />
    <ClInclude Include="..\..\..\publish\ksoftmgr\ksoftmgrenginewarp2.h" />
    <ClInclude Include="bkwinres.h" />
    <ClInclude Include="bkwin\KEdit.h" />
    <ClInclude Include="bkwin\KTipEdit.h" />
    <ClInclude Include="dlg\CBkMenu.h" />
    <ClInclude Include="dlg\DGRecycleScanTrashPop.h" />
    <ClInclude Include="dlg\KAudioAbnormalPop.h" />
    <ClInclude Include="dlg\KAutoSpeedUpPop.h" />
    <ClInclude Include="dlg\KBlueScreenPopDlg.h" />
    <ClInclude Include="dlg\KCleanAdSuggestPopDlg.h" />
    <ClInclude Include="dlg\KCleanTrashSettingMenu.h" />
    <ClInclude Include="dlg\KComfirmPopDlg.h" />
    <ClInclude Include="dlg\KCommonPopDlg.h" />
    <ClInclude Include="dlg\KDefragPopDlg.h" />
    <ClInclude Include="dlg\KDevMoveoutOfflinePopDlg.h" />
    <ClInclude Include="dlg\KDGDriverManagerPopDlg.h" />
    <ClInclude Include="dlg\KDiskMgrPop.h" />
    <ClInclude Include="dlg\KDiskMgrPopWinToast.h" />
    <ClInclude Include="dlg\KDiskslowPopDlg.h" />
    <ClInclude Include="dlg\KDlgTimeoutBase.h" />
    <ClInclude Include="dlg\KDriverManagerPopDlg.h" />
    <ClInclude Include="dlg\KDumpRightMenuPopDlg.h" />
    <ClInclude Include="dlg\KExplorerPopDlg.h" />
    <ClInclude Include="dlg\KInternetExceptionPop.h" />
    <ClInclude Include="dlg\KInternetProtectPluginPop.h" />
    <ClInclude Include="dlg\KMachineLimitPopDlg.h" />
    <ClInclude Include="dlg\KOfflinePopDlg.h" />
    <ClInclude Include="dlg\KPopBase.h" />
    <ClInclude Include="dlg\KPrinterAbnormalPop.h" />
    <ClInclude Include="dlg\KRcmdPopCondition.h" />
    <ClInclude Include="dlg\KSatisfactionPopDlg.h" />
    <ClInclude Include="dlg\KScreenCaptureTaskbarPopDlg.h" />
    <ClInclude Include="dlg\KScreenRecordPop.h" />
    <ClInclude Include="dlg\KSearchboxPop.h" />
    <ClInclude Include="dlg\KSemAuthorityPopDlg.h" />
    <ClInclude Include="dlg\KSoftDeepCleanPopDlg.h" />
    <ClInclude Include="dlg\KSysOptimizeTaskBarPop.h" />
    <ClInclude Include="dlg\KSystemTimeExceptionPop.h" />
    <ClInclude Include="dlg\KTemperatureMonitorTipDlg.h" />
    <ClInclude Include="dlg\KTemperatureMonitorTipVipDlg.h" />
    <ClInclude Include="dlg\KTemperatureMonitorTipWintoastPop.h" />
    <ClInclude Include="dlg\KVipFreePackPopDlg.h" />
    <ClInclude Include="KPopWarp.h" />
    <ClInclude Include="KReporter.h" />
    <ClInclude Include="KUtil.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="SemAuthority\KSafeApiHelper.h" />
    <ClInclude Include="SemAuthority\KSemAuthorityPopMgr.h" />
    <ClInclude Include="SemAuthority\UserChoice_hash.h" />
    <ClInclude Include="stdafx.h" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\..\..\..\..\kis\src\GUI\publish\bkres\bkres.rc" />
    <ResourceCompile Include="kplant_pure_vip_noad_pop.rc" />
  </ItemGroup>
  <ItemGroup>
    <Xml Include="res\assoftmgrpop.xml" />
    <Xml Include="res\audioabnormal\KAudioAbnormalPluginPop.xml" />
    <Xml Include="res\bluescreen\dlg_bluescreen.xml" />
    <Xml Include="res\clean_ad_suggest_dlg.xml" />
    <Xml Include="res\def_skin.xml" />
    <Xml Include="res\def_string.xml" />
    <Xml Include="res\def_style.xml" />
    <Xml Include="res\diskslow\dlg_diskslow.xml" />
    <Xml Include="res\dlg_cleantrash_setting.xml" />
    <Xml Include="res\dlg_dg_scan_recycle.xml" />
    <Xml Include="res\dlg_setting_menu.xml" />
    <Xml Include="res\dlg_sysslim_clean_pop2.xml" />
    <Xml Include="res\dlg_sysslim_clean_pop2_new.xml" />
    <Xml Include="res\dlg_sysslim_clean_pop3.xml" />
    <Xml Include="res\internetexception\KInternetExceptionPop.xml" />
    <Xml Include="res\kautospeeduppop.xml" />
    <Xml Include="res\kcommonpop.xml" />
    <Xml Include="res\kdefragpop.xml" />
    <Xml Include="res\kdefragpop_new.xml" />
    <Xml Include="res\KDiskManager.xml" />
    <Xml Include="res\kdrivermanagerpop.xml" />
    <Xml Include="res\kdrivermanagerpop_dg_new.xml" />
    <Xml Include="res\kdrivermanagerpop_new.xml" />
    <Xml Include="res\kexplorerpop.xml" />
    <Xml Include="res\kfiledestroypop.xml" />
    <Xml Include="res\KInternetProtectPluginPop.xml" />
    <Xml Include="res\klsoftmgr_antireinstall_pop.xml" />
    <Xml Include="res\klsoftmgr_antireinstall_ret_pop.xml" />
    <Xml Include="res\klsoftmgr_pop.xml" />
    <Xml Include="res\kprcycleanerpop.xml" />
    <Xml Include="res\kscreencapturetaskbarpop.xml" />
    <Xml Include="res\ksoftdeepcleanpop.xml" />
    <Xml Include="res\KSystemTimeExceptionPop.xml" />
    <Xml Include="res\KTemperatureMonitorHightTipPop.xml" />
    <Xml Include="res\KTemperatureMonitorTipPop.xml" />
    <Xml Include="res\KTemperatureMonitorTipVipPop.xml" />
    <Xml Include="res\machine_limit_dlg.xml" />
    <Xml Include="res\planet_vip_noad_dlg.xml" />
    <Xml Include="res\printerabnormal\KPrinterAbnormalPluginPop.xml" />
    <Xml Include="res\satisfaction\dlg_satisfaction.xml" />
    <Xml Include="res\winoptimizetaskbar\KWinOptimizeTaskBarPop.xml" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>