﻿<layer width="600" height="440" appwin="0" noborder="0" resize="0" dpiaware="1">
  <header width="full" height="full" crbg="FFFFFF">
    <img pos="0,0,-0,-0" class="semauthority_bg"/>

    <!-- 标题栏 -->
    <dlg pos="0,0,-0,40">
      <img pos="16,0" skin="semauthority_logo"/>
      <imgbtn id="100" pos="-40,8,-16,-8" skin="btn_close_white" class="cursor_hand"/>
    </dlg>

    <!-- 软件名称 -->
    <dlg id="101" pos="0,54,-0,86"><!--101：动态计算位置-->
      <storageimage id="102" pos="0,0,32,32" show="0"/>
      <img id="103" pos="0,0,32,32" skin="semauthority_default_icon" show="1"/>

      <text id="104" pos="42,1,-0,30" class="semauthority_softname_text"/><!--“%s”安装完成-->
      <text id="105" pos="42,1" class="semauthority_softname_text"show="0"/> <!--辅助计算101的x值-->
    </dlg>

    <!-- 权限管理-->
    <dlg id="1000" pos="0,86,-0,-0" show="1">
      <!-- 步骤标签 -->
      <dlg pos="0,10,-0,30">
        <text pos="202,0,-0,16" class="semauthority_step_text_grey">安装软件</text>
        <img pos="254,3" skin="semauthority_step"/>

        <text pos="270,0,-0,16" class="semauthority_step_text_blue">···</text>
        <text pos="284,0,-0,16" class="semauthority_step_text_blue">管理权限</text>

        <text pos="336,0,-0,16" class="semauthority_step_text_grey">···</text>
        <text pos="350,0,-0,16" class="semauthority_step_text_grey">清理残留</text>
      </dlg>

      <text pos="80,50,-0,70" class="semauthority_subpage_title_black">请确定是否允许软件使用以下权限</text>

      <dlg pos="0,84,-0,-0">
        <dlg pos="0,0,-0,20"><!-- 位置1="0,0,-0,20" -->
          <img pos="80,2" skin="semauthority_boot"/>
          <text pos="106,0,246,20" class="semauthority_item_title">添加开机启动项</text>
          <text pos="252,2,432,18" class="semauthority_item_subtitle">导致开机速度变慢</text>
          <text id="1100" pos="444,2,500,18" class="semauthority_item_subtitle" show="1">未发现</text>
          <dlg id="1101" pos="444,0,-0,-0" show="0">
            <imgbtn id="1102" pos="0,2" skin="semauthority_switch_on" class="hand"/>
            <text id="1103" pos="40,2,90,18" class="semauthority_item_subtitle" transparent="1">已允许</text>
          </dlg>
        </dlg>

        <dlg pos="0,34,-0,54" show="1"><!--透权未处理，不相识。 位置2="0,34,-0,54" -->
          <img pos="80,2" skin="semauthority_popwidget"/>
          <text pos="106,0,246,20" class="semauthority_item_title">弹窗推广</text>
          <text pos="252,2,432,18" class="semauthority_item_subtitle">右下角弹窗等推广打扰</text>
          <text id="1200" pos="444,2,500,18" class="semauthority_item_subtitle" show="1">未发现</text>
          <dlg id="1201" pos="444,0,-0,-0" show="0">
            <imgbtn id="1202" pos="0,2" skin="semauthority_switch_off" class="hand"/>
            <text id="1203" pos="40,2,90,18" class="semauthority_item_subtitle" transparent="1">已阻止</text>
          </dlg>
        </dlg>

        <dlg pos="0,68,-0,88"><!-- 位置3="0,68,-0,88" -->
          <img pos="80,2" skin="semauthority_rightmenu"/>
          <text pos="106,0,246,20" class="semauthority_item_title">添加右键菜单</text>
          <text pos="252,2,432,18" class="semauthority_item_subtitle">导致鼠标右键菜单拥挤</text>
          <text id="1300" pos="444,2,500,18" class="semauthority_item_subtitle" show="1">未发现</text>
          <dlg id="1301" pos="444,0,-0,-0" show="0">
            <imgbtn id="1302" pos="0,2" skin="semauthority_switch_on" class="hand"/>
            <text id="1303" pos="40,2,90,18" class="semauthority_item_subtitle" transparent="1">已允许</text>
          </dlg>
        </dlg>

        <dlg pos="0,102,-0,122"><!-- 位置4="0,102,-0,122" -->
          <img pos="80,2" skin="semauthority_fileassociation"/>
          <text pos="106,0,246,20" class="semauthority_item_title">默认应用修改</text>
          <text pos="252,2,432,18" class="semauthority_item_subtitle">改变常用文件的打开方式</text>
          <text id="1400" pos="444,2,500,18" class="semauthority_item_subtitle" show="1">未发现</text>
          <dlg id="1401" pos="444,0,-0,-0" show="0">
            <imgbtn id="1402" pos="0,2" skin="semauthority_switch_on" class="hand"/>
            <text id="1403" pos="40,2,90,18" class="semauthority_item_subtitle" transparent="1">已允许</text>
          </dlg>
        </dlg>
      </dlg>

      <imgbtn id="1001" pos="|-60,246,|60,294" skin="semauthority_click" class="cursor_hand"/>
    </dlg>

    <!--垃圾清理-->
    <dlg id="2000" pos="0,86,-0,-0" show="0">
    <!-- 步骤标签 -->
      <dlg pos="0,10,-0,30">
        <text pos="194,0,-0,16" class="semauthority_step_text_grey">安装软件</text>
        <img pos="246,3" skin="semauthority_step"/>

        <text pos="262,0,-0,16" class="semauthority_step_text_grey">···</text>
        <text pos="276,0,-0,16" class="semauthority_step_text_grey">管理权限</text>
        <img pos="328,3" skin="semauthority_step"/>

        <text pos="344,0,-0,16" class="semauthority_step_text_blue">···</text>
        <text pos="358,0,-0,16" class="semauthority_step_text_blue">清理残留</text>
      </dlg>


      <richtext2 id="2001" pos="90,71,-0,91"><!-- 默认值-->
        <text class="semauthority_subpage_title_black">立即清理残留安装包等垃圾文件，平均释放</text>
        <offset length="4"></offset>
        <text class="semauthority_subpage_title_orange">3.9GB</text>
        <offset length="4"></offset>
        <text class="semauthority_subpage_title_black">磁盘空间</text>
      </richtext2>

      <dlg pos="0,106,-0,-0">
        <dlg pos="80,0,160,88">
          <img  pos="10,0" skin="semauthority_cleaner_inst"/>
          <text pos="0,68,-0,88" class="semauthority_cleaner_item_text">安装包残留</text>
        </dlg>

        <dlg pos="200,0,280,88">
          <img  pos="10,0" skin="semauthority_cleaner_network"/>
          <text pos="0,68,-0,88" class="semauthority_cleaner_item_text">上网残留</text>
        </dlg>

        <dlg pos="320,0,400,88">
          <img  pos="10,0" skin="semauthority_cleaner_system"/>
          <text pos="0,68,-0,88" class="semauthority_cleaner_item_text">系统垃圾</text>
        </dlg>

        <dlg pos="440,0,520,88">
          <img  pos="10,0" skin="semauthority_cleaner_soft"/>
          <text pos="0,68,-0,88" class="semauthority_cleaner_item_text">其它软件</text>
        </dlg>
      </dlg>

      <imgbtn id="2002" pos="|-60,246,|60,294" skin="semauthority_cleaner_click" class="cursor_hand"/>
    </dlg>
  </header>
</layer>
