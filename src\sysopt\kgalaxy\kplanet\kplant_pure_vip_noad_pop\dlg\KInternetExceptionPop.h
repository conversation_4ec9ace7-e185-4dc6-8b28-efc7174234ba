 #pragma once
#include "KPopBase.h"

class KInternetExceptionPop : public CBkDialogViewEx,
                              public IShadowPaintHook,
                              public KPopBase
{
public:
    enum PopType
    {
        PT_UNKNOWN            = 0,
        PT_PROXY_OPEN         = 1,
        PT_WLAN_SVC_EXCEPTION = 2,
        PT_END                = 3
    };


	KInternetExceptionPop(kplanet::KPopTask *pHost);
    ~KInternetExceptionPop(void);

#define UM_MSG_AFTERINIT (WM_USER + 1)

    enum enumResID
    {
        ID_TEXT_NEVER_SHOW = 120,
        ID_BTN_CLOSE       = 121,
        ID_BTN_OK          = 122,
        ID_IMG_ICON        = 123,
        ID_TEXT_MAIN_TITLE = 124,
        ID_BTN_CANCEL      = 125,
        ID_TEXT_SUB_TITLE  = 126,
    };

public:
    BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
    BK_NOTIFY_ID_COMMAND(ID_TEXT_NEVER_SHOW, OnBtnNeverShow)
    BK_NOTIFY_ID_COMMAND(ID_BTN_CLOSE, OnBtnClose)
    BK_NOTIFY_ID_COMMAND(ID_BTN_OK, OnBtnOK)
    BK_NOTIFY_ID_COMMAND(ID_BTN_CANCEL, OnBtnCancel)
    BK_NOTIFY_MAP_END()

    BEGIN_MSG_MAP_EX(KInternetExceptionPop)
    MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
    MSG_WM_INITDIALOG(OnInitDialog)
    MSG_WM_TIMER(OnTimer)
    MSG_WM_MOUSEMOVE(OnMouseMove)
    MESSAGE_HANDLER(UM_MSG_AFTERINIT, OnMessageAfterInit)
    CHAIN_MSG_MAP(CBkDialogViewEx)
    END_MSG_MAP()

protected:
    // public KPopBase
    virtual BOOL    Init(CString strJsonParam);
    virtual BOOL    UnInit();
    virtual DWORD   GetPopId();
    virtual CString GetPopName();
    virtual BOOL    CanShow();
    virtual BOOL    Show();

    // public IShadowPaintHook
    virtual BOOL NotifyShadowPaint(HDC hDC, CRect &rct);

protected:
    LRESULT OnInitDialog(HWND /*hWnd*/, LPARAM /*lParam*/);
    LRESULT OnMessageAfterInit(UINT /*uMsg*/, WPARAM /*wParam*/,
                               LPARAM /*lParam*/, BOOL & /*bHandled*/);
    void    OnTimer(UINT_PTR nIDEvent);
    void    OnMouseMove(UINT nFlags, CPoint point);

    void OnBtnClose();
    void OnBtnNeverShow();
    void OnBtnOK();
    void OnBtnCancel();

private:
    void OnEndDlgImpl(UINT uRetCode);
    void _Report(int action);
    BOOL _IsClickedNeverShow();
    void ResiteDlgCommonImpl(int nWidth, int nHeight);
    void _UpdateUI();
    void _GotoFix();
    void _AutoClose();

private:
    kplanet::KPopTask *m_pHost;
    int                m_nPopType;
    DWORD              m_dwMouseMoveLastTick;
};
