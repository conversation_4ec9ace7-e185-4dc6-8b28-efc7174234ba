#pragma once
#include <atlbase.h>
namespace KRcmdPopCondition
{
    BOOL IsNoShowAnymore();
    BOOL SetNoShowAnymore();
    DWORD GetNoShowDays();
    BOOL SetNoshowDays(DWORD dwDays);
    INT64 GetLastCallTime();
    BOOL  SetLastCallTime();
    INT64 GetLastShowTime();
    BOOL SetLastShowTime();

    BOOL SetPopRegisterValue(LPCTSTR szValueName, DWORD dwValue);
    BOOL SetPopRegisterInt64Value(LPCTSTR szValueName,INT64 nValue);
    BOOL GetPopRegisterValue(LPCTSTR szValueName, DWORD &dwValue);
    BOOL GetPopRegisterInt64Value(LPCTSTR szValueName, INT64 &nValue);

};

