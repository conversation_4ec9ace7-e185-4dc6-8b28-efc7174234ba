#pragma once

#include "include/framework/KTinyXml.h"
#include "include/framework/KCreateXmlElementFunc.h"
#include "kis\src\GUI\publish\bkwin\CBkDialogViewImplEx.h"
#include <vector>

#pragma warning(disable:4018)
#pragma warning(disable:4244)
#pragma warning(disable:4267)

#define MENU_ITEM_HEIGHT    30
#define MENU_ITEM_WIDTH     150

struct MenuItem
{
    int id;
    CString strTitle;
    CString strTip;
    CString strIconSkin;
};
typedef std::map<int, MenuItem> MenuItems;

class BKMenuCallBack
{
public:
    virtual ~BKMenuCallBack(){}
    virtual void MenuItemClick(int menuId){}
};

class CBKMenu : public CBkDialogViewImplEx<CBKMenu>, public IShadowPaintHook
{
public:
    enum RESID
    {
        IDC_LISTWND = 1001,
    };

    enum UIElementDistance
    {
        text_distance = 10,
    };

    CBKMenu() : CBkDialogViewImplEx<CBKMenu>(31079)
    ,_pCb(NULL)
    ,_itemHeight(MENU_ITEM_HEIGHT)
    ,_itemWidth(MENU_ITEM_WIDTH)
    {
        SetDrawBorder(FALSE);
        SetShadowSize(28);
    }

    ~CBKMenu()
    {
    }

    void AddLisener(BKMenuCallBack* pCb)
    {
        _pCb = pCb;
    }

    void SetItemWidthAndHeight(int iWidth = MENU_ITEM_WIDTH, int iHeight = MENU_ITEM_HEIGHT)
    {
        _itemWidth = iWidth;
        _itemHeight = iHeight;
    }

    BOOL OnInitDialog(HWND wParam, LPARAM lParam)
    {
        SetShadowPaintHook(this);
        ModifyStyleEx(0, WS_EX_TOOLWINDOW);
        return TRUE;
    }

    void AddItems(MenuItems& items)
    {
        _menuItems = items;
        DeleteAllListItem(IDC_LISTWND);
        for (MenuItems::iterator it = _menuItems.begin(); it != _menuItems.end(); ++it)
        {
            _CreateItem(it->second);
        }
        _UpdateMenuSize();
    }

    void AddItem(MenuItem& item)
    {
        _menuItems[item.id] = item;
        _CreateItem(item);
        _UpdateMenuSize();
    }

    BOOL PopUp(CPoint& point)
    {
        _ResetMenuUIState();
        SetWindowPos(HWND_TOP, point.x, point.y, 0, 0, SWP_SHOWWINDOW | SWP_NOSIZE);
        ShowWindow(SW_SHOWNORMAL);
        return TRUE;
    }

    BOOL OnNcActivate(BOOL bActive)
    {
        ShowWindow(bActive ? SW_SHOWNORMAL : SW_HIDE);
        return TRUE;
    }

    void OnSysCommand(UINT nID, CPoint point)
    {
        SetMsgHandled(FALSE);

        switch (nID & 0xFFF0)
        {
        case SC_CLOSE:
            SetMsgHandled(TRUE);
            ShowWindow(SW_HIDE);
            break;
        }
    }

    virtual BOOL NotifyShadowPaint( HDC hDC, CRect& rct )
    {
        WINDOWPOS wndPos = {0};
        CBkExpendImg exmPandImg;

        wndPos.x = rct.left;
        wndPos.y = rct.top;
        wndPos.cx = rct.Width();
        wndPos.cy = rct.Height();

        exmPandImg.SetAttribute("pos", "0,0,-0,-0", TRUE);
        exmPandImg.BkSendMessage(WM_WINDOWPOSCHANGED, NULL, (LPARAM)&wndPos);

        exmPandImg.SetAttribute("margin_left", "28", TRUE);
        exmPandImg.SetAttribute("margin_right", "28", TRUE);
        exmPandImg.SetAttribute("margin_top", "28", TRUE);
        exmPandImg.SetAttribute("margin_bottom", "28", TRUE);
        exmPandImg.SetAttribute("skin", "cnm_showdow", TRUE);
        exmPandImg.BkSendMessage(WM_PAINT, (WPARAM)hDC);

        return FALSE;
    }

protected:
    void _CreateItem(MenuItem& item)
    {
        KTinyXml tinyXml;
        if (NULL == tinyXml.Open("Root", TRUE))
            return;

        KCreateXmlElementFunc XmlElFunc(tinyXml);
        XmlElFunc.AddTinyChild("listitem", 0, NULL);
        {
            KTinyXmlRememberPos(tinyXml);
            XmlElFunc().Write("height", _itemHeight);
            XmlElFunc().Write("crbg", "ffffff");
            XmlElFunc.AddTinyChild("dlg", item.id, "0,0,-0,-0", "1", NULL, "cm_menu_bg");
            XmlElFunc.AddTinyChild("text", item.id + text_distance, "0,0,-0,-0", "1", NULL, "cm_menu_text");
            XmlElFunc().Write("transparent", "1");
            XmlElFunc().WriteText(item.strTitle);

            if (_menuItems.size() != 1)
            {
                XmlElFunc.AddTinySibling("hr", 0, "10,0,-10,0", "1");
                XmlElFunc().Write("crbg", "CECECE");
            }
        }
        AppendListItem(IDC_LISTWND, tinyXml.GetElement(), -1, FALSE);
    }

    void _UpdateMenuSize()
    {
        int iNewHeight = _itemHeight * _menuItems.size();
        SetWindowPos(NULL, 0,0, _itemWidth, iNewHeight, SWP_NOMOVE|SWP_NOZORDER);
    }

    void _ResetMenuUIState()
    {
        int iListItem = 0;
        for (MenuItems::iterator it = _menuItems.begin(); it != _menuItems.end(); ++it)
        {
            SetListItemChildCtrlAttribute(IDC_LISTWND, iListItem++, it->first + text_distance, "class", "cm_menu_text");
        }
    }

    void OnListItemChildCtrlLButtonUpEx(int nListItem, UINT uItemID, LPCSTR szListItemChildClass)
    {
        if (_pCb)
            _pCb->MenuItemClick(uItemID);

        ShowWindow(SW_HIDE);
    }

    void OnListItemChildCtrlMouseHoverEx(int nListItem, UINT uItemID, LPCSTR szListItemChildClass)
    {
        _ResetMenuUIState();
        SetListItemChildCtrlAttribute(IDC_LISTWND, nListItem, uItemID + text_distance, "class", "cm_menu_text_hover");;
    }

    void OnListItemChildCtrlMouseLeaveEx(int nListItem, UINT uItemID, LPCSTR szListItemChildClass)
    {
        _ResetMenuUIState();
        SetListItemChildCtrlAttribute(IDC_LISTWND, nListItem, uItemID + text_distance, "class", "cm_menu_text");;
    }

    BEGIN_MSG_MAP_EX(CBKMenu)
        MSG_WM_NCACTIVATE(OnNcActivate)
        MSG_WM_SYSCOMMAND(OnSysCommand)
        MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
        CHAIN_MSG_MAP(CBkDialogViewImplEx<CBKMenu>)
        MSG_WM_INITDIALOG(OnInitDialog)
    END_MSG_MAP()

    BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
        BK_LISTWND_NOTIFY_BEGIN(IDC_LISTWND)
        BK_LISTWND_LISTITEM_CHILD_LBUTTONUP_EX(OnListItemChildCtrlLButtonUpEx)
        BK_LISTWND_LISTITEM_CHILD_MOUSEHOVER_EX(OnListItemChildCtrlMouseHoverEx)
        BK_LISTWND_LISTITEM_CHILD_MOUSELEAV_EXE(OnListItemChildCtrlMouseLeaveEx)
        BK_LISTWND_NOTIFY_END()
    BK_NOTIFY_MAP_END()

private:
    MenuItems _menuItems;
    BKMenuCallBack* _pCb;
    int _itemHeight;
    int _itemWidth;
};

#pragma warning(default:4018)
#pragma warning(default:4244)
#pragma warning(default:4267)