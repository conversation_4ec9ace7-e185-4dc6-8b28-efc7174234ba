#ifndef __K_EXPLORER_POP_DIALOG_H__
#define __K_EXPLORER_POP_DIALOG_H__

#include "KPopBase.h"
#include "CBkMenu.h"
#include "publish/popcenter/kpopclientwarp.h"

class KExplorerPopDlg : public CBkDialogViewEx,
    public IShadowPaintHook, public KPopBase, public BKMenuCallBack{
public:
    KExplorerPopDlg(kplanet::KPopTask* host);
    ~KExplorerPopDlg();

#define UM_MSG_AFTERINIT   (WM_USER + 1)

    enum eControlID {
        ID_CLOSE = 100,
        ID_BTN_SETTING = 102,
        ID_BTN_OPEN = 103,
    };

    enum enum_popclick
    {
        em_pop_click_unkonw = 0,
        em_pop_click_show = 1,
        em_pop_click_btn_main = 2,
        em_pop_click_btn_close = 3,
        em_pop_click_btn_noshowdays = 4,
        em_pop_click_btn_noanymore = 5,
    };

protected:
    virtual void MenuItemClick(int menuId);
    virtual BOOL NotifyShadowPaint(HDC hDC, CRect& rect);

public:  // KPopBase methods override
    virtual BOOL Init(CString strJsonParam);
    virtual BOOL UnInit();
    virtual DWORD GetPopId();
    virtual CString GetPopName();
    virtual BOOL CanShow();
    virtual BOOL Show();

protected:
    BOOL OnInitDialog(CWindow wndFocus, LPARAM lInitParam);
    void OnBtnClose();
    void OnBtnOpen();
    void OnBtnSetting();
    void OnTimer( UINT_PTR nIDEvent );
    void OnClose(LPCWSTR lpszReason, KPopCenterDef::PopHideReasonCode nReason);
    void CreateMenu();

    void AddMenuTask(LPCTSTR lpszText, int id, LPCTSTR lpszTip = NULL, LPCTSTR lpszIconSkin = NULL);
    LRESULT OnMessageAfterInit(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/);
    BOOL Reportduba_explorer_pop(int nClick);

private:

    BK_NOTIFY_MAP(IDC_RICHVIEW_WIN_EX)
        BK_NOTIFY_ID_COMMAND(ID_CLOSE, OnBtnClose)
        BK_NOTIFY_ID_COMMAND(ID_BTN_OPEN, OnBtnOpen)
        BK_NOTIFY_ID_COMMAND(ID_BTN_SETTING, OnBtnSetting)
        BK_NOTIFY_MAP_END()

        BEGIN_MSG_MAP_EX(KExplorerPopDlg)
        MSG_BK_NOTIFY(IDC_RICHVIEW_WIN_EX)
        MSG_WM_INITDIALOG(OnInitDialog)
        MESSAGE_HANDLER(UM_MSG_AFTERINIT, OnMessageAfterInit)
        MSG_WM_TIMER(OnTimer)
        CHAIN_MSG_MAP(CBkDialogViewEx)
        END_MSG_MAP()

protected:
    CBKMenu m_settingMenu;

    kplanet::KPopTask* host_;
    std::wstring m_strToken;
    std::wstring m_strOpenId;
};

#endif