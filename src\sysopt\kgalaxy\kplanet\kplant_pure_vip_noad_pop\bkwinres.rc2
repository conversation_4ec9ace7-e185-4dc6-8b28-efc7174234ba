#pragma once

//////////////////////////////////////////////////////////////////////////
// RES
DEFINE_SKIN("res\\def_skin.xml")
DEFINE_STYLE("res\\def_style.xml")
DEFINE_STRING("res\\def_string.xml")

//////////////////////////////////////////////////////////////////////////
// XML
DEFINE_XML(IDR_CLEAN_AD_SUGGEST_DLG, 1100, "res\\clean_ad_suggest_dlg.xml")
DEFINE_XML(IDR_MACHINE_LIMIT_DLG, 1101, "res\\machine_limit_dlg.xml")
DEFINE_XML(IDR_DLG_SETTING_MENU, 31079, "res\\dlg_setting_menu.xml")
DEFINE_XML(VIP_FREEPACK_POP, 1201, "res\\planet_vip_freepack_dlg.xml")
DEFINE_XML(IDR_OFFLINE_POP, 1202, "res\\offline_pop.xml")
DEFINE_XML(IDR_DEV_MOVEOUT_OFFLINE_POP, 1203, "res\\dev_moveout_offline_pop.xml")
DEFINE_XML(KSCREENRECORD_POP, 1204, "res\\kscreenrecordpop.xml")
DEFINE_XML(KAUTOSPEEDUP_POP, 1205, "res\\kautospeeduppop.xml")
DEFINE_XML(KDEFRAG_POP, 1206, "res\\kdefragpop.xml")
DEFINE_XML(KSOFTDEEPCLEAN_POP, 1207, "res\\ksoftdeepcleanpop.xml")
DEFINE_XML(KCOMMON_POP, 1208, "res\\kcommonpop.xml")
DEFINE_XML(KDRIVERMANAGER_POP, 1209, "res\\kdrivermanagerpop.xml")
DEFINE_XML(KFILEDESTORY_POP, 1210, "res\\kfiledestroypop.xml")
DEFINE_XML(KDRIVERMANAGER_NEW_POP, 1211, "res\\kdrivermanagerpop_new.xml")
DEFINE_XML(KSCREENCAPTURE_TASKBAR_POP, 1215, "res\\kscreencapturetaskbarpop.xml")
DEFINE_XML(KEXPLORER_POP, 1213, "res\\kexplorerpop.xml")
DEFINE_XML(IDR_DLG_SYSSLIM_CLEAN_DLG2, 1217, "res\\dlg_sysslim_clean_pop2.xml")
DEFINE_XML(IDR_DLG_SYSSLIM_CLEAN_DLG3, 1218, "res\\dlg_sysslim_clean_pop3.xml")
DEFINE_XML(KDEFRAG_POP2, 1219, "res\\kdefragpop_new.xml")
DEFINE_XML(IDR_DLG_SYSSLIM_CLEAN_DLG2_NEW, 1220, "res\\dlg_sysslim_clean_pop2_new.xml")
DEFINE_XML(ASSOFTMGR_POP, 1216, "res\\assoftmgrpop.xml")
DEFINE_XML(IDR_DLG_KLSOFTMGR_POP, 1221, "res\\klsoftmgr_pop.xml")
DEFINE_XML(IDR_DLG_KLSOFTMGR_ANTIREINSTALL_POP, 1222, "res\\klsoftmgr_antireinstall_pop.xml")
DEFINE_XML(IDR_DLG_KLSOFTMGR_ANTIREINSTALL_POP_RET, 1223, "res\\klsoftmgr_antireinstall_ret_pop.xml")
DEFINE_XML(IDR_DLG_KTEMPERATURE_MONITOR_TIP_POP, 1224, "res\\KTemperatureMonitorTipPop.xml")
DEFINE_XML(IDR_DLG_KTEMPERATURE_MONITOR_VIP_TIP_POP, 1225, "res\\KTemperatureMonitorTipVipPop.xml")
DEFINE_XML(IDR_DLG_KTEMPERATURE_MONITOR_HIGHTEMP_TIP_POP, 1226, "res\\KTemperatureMonitorHightTipPop.xml")
//DEFINE_XML(IDR_DLG_KINTERNET_PROTECT_POP, 1227, "res\\KInternetProtectPop.xml")
DEFINE_XML(IDR_DLG_KDISK_MANAGER_POP, 1228, "res\\KDiskManager.xml")
//DEFINE_XML(IDR_DLG_KDISK_MANAGER_POP, 1229, "res\\KDiskRepeatManager.xml")
//DEFINE_XML(IDR_DLG_KDISK_MANAGER_POP, 1230, "res\\KDiskWechatManager.xml")
//DEFINE_XML(IDR_DLG_KDISK_MANAGER_POP, 1231, "res\\KDiskQQFileManager.xml")
DEFINE_XML(IDR_DLG_KBSOD_POP, 1232, "res\\bluescreen\\dlg_bluescreen.xml")
DEFINE_XML(IDR_DLG_KDISKSLOW_POP, 1233, "res\\diskslow\\dlg_diskslow.xml")
DEFINE_XML(IDR_DLG_KSATISFACTION_POP, 1234, "res\\satisfaction\\dlg_satisfaction.xml")
DEFINE_XML(IDR_DLG_KDUMPRIGHTMENU_POP, 1235, "res\\dumprightmenu\\dlg_dumprightmenu_pop.xml")
//DEFINE_XML(IDR_DLG_USB_AUTOPLAY_A_POP, 1236, "res\\usb\\dlg_usb_view_a.xml")
//DEFINE_XML(IDR_DLG_USB_AUTOPLAY_B_POP, 1237, "res\\usb\\dlg_usb_view_b.xml")
DEFINE_XML(IDR_DLG_SEM_AUTHORITY_POP,  1238, "res\\semauthority\\dlg_semauthority_pop.xml")
DEFINE_XML(IDR_DLG_SYSTEM_TIME_EXCEPTION_POP,  1240, "res\\KSystemTimeExceptionPop.xml")
DEFINE_XML(IDR_DLG_INTERNET_PROTECT_PLUGIN_POP,  1241, "res\\KInternetProtectPluginPop.xml")
DEFINE_XML(IDR_DLG_AUDIO_ABNORMAL_PLUGIN_POP,  1242, "res\\audioabnormal\\KAudioAbnormalPluginPop.xml")
DEFINE_XML(IDR_DLG_PRINTER_ABNORMAL_POP,  1243, "res\\printerabnormal\\KPrinterAbnormalPluginPop.xml")
DEFINE_XML(IDR_DLG_SEARCHBOX_POP,  1244, "res\\searchbox\\KSearchboxPluginPop.xml")
DEFINE_XML(IDR_DLG_WINOPTIMIZE_PRIVACY_POP,  1245, "res\\winoptimize\\KWinOptimizePrivacyPop.xml")
DEFINE_XML(IDR_DLG_WINOPTIMIZE_TASKBAR_POP,  1246, "res\\winoptimizetaskbar\\KWinOptimizeTaskBarPop.xml")
DEFINE_XML(IDR_DLG_INTERNET_EXCEPTION_POP,  1247, "res\\internetexception\\KInternetExceptionPop.xml")
DEFINE_XML(IDR_KDRIVERMANAGER_DG_POP, 1248, "res\\kdrivermanagerpop_dg_new.xml")
DEFINE_XML(IDR_DG_RECYCLES_POP, 1249, "res\\dlg_dg_scan_recycle.xml")

//////////////////////////////////////////////////////////////////////////
// PNG
DEFINE_PNG(IDP_CLOSE, 1001, "res\\close.png")
DEFINE_PNG(IDP_SHADOW, 1002, "res\\shadow.png")
DEFINE_PNG(IDP_BODY_BK, 1003, "res\\body_bg.png")
DEFINE_PNG(IDP_MAIN_BUTTON, 1004, "res\\main_button.png")
DEFINE_PNG(IDP_HEADER_BK, 1005, "res\\header_bg.png")
DEFINE_PNG(IDP_LEFT_IMG, 1006, "res\\left_img.png")
DEFINE_PNG(IDP_SETTING_IMG, 1007, "res\\setting.png")

//machine limit resources
DEFINE_PNG(IDP_BG,  1010,   "res\\bg.png")
DEFINE_PNG(IDP_WARNING_ICON,    1011,   "res\\warning_icon.png")
DEFINE_PNG(IDP_BTN_LOGIN,       1012,   "res\\btn_login.png")
DEFINE_PNG(IDP_BTN_CONFIRM,     1013,   "res\\btn_confirm.png")
DEFINE_PNG(IDP_1014,            1014,   "res\\skin_vip_freepack_bg.png")
DEFINE_PNG(IDP_1015,            1015,   "res\\main_button2.png")
DEFINE_PNG(IDP_1016,            1016,   "res\\img_bg2.png")
DEFINE_PNG(IDP_1017,            1017,   "res\\btn_close.png")
DEFINE_PNG(IDP_1018,            1018,   "res\\img_logo.png")
DEFINE_PNG(IDP_1019,            1019,   "res\\btn_red.png")
DEFINE_PNG(IDP_1020,            1020,   "res\\btn_orange.png")
DEFINE_PNG(IDP_1021,            1021,   "res\\img_warning_icon2.png")
DEFINE_PNG(IDP_1022,            1022,   "res\\img_warning_icon3.png")
DEFINE_PNG(IDP_1023,            1023,   "res\\btn_green.png")
DEFINE_PNG(IDP_1024,            1024,   "res\\btn_white.png")
DEFINE_PNG(IDP_1025,            1025,   "res\\btn_orange2.png")

//screenrecord pop resources
DEFINE_PNG(IDP_1026,            1026,   "res\\btn_ok.png")
DEFINE_PNG(IDP_1027,            1027,   "res\\btn_close2.png")
DEFINE_PNG(IDP_1028,            1028,   "res\\screen_record_pop_bg.png")
DEFINE_PNG(IDP_1029,            1029,   "res\\btn_close3.png")
DEFINE_PNG(IDP_1030,            1030,   "res\\setting_img2.png")

//auto speed up pop resoureces
DEFINE_PNG(IDP_1031,            1031,   "res\\auto_clean_bg.png")
DEFINE_PNG(IDP_1032,            1032,   "res\\auto_clean_left.png")
DEFINE_PNG(IDP_1033,            1033,   "res\\auto_clean_dropDown.png")
DEFINE_PNG(IDP_1034,            1034,   "res\\auto_clean_close.png")

//defrag pop resoureces
DEFINE_PNG(IDP_1035,            1035,   "res\\defrag_bg.png")
DEFINE_PNG(IDP_1036,            1036,   "res\\defrag_clean.png")
DEFINE_PNG(IDP_1037,            1037,   "res\\defrag_bg_dg.png")
DEFINE_PNG(IDP_1038,            1038,   "res\\defrag_bg_cm.png")

DEFINE_PNG(IDP_1039,            1039,   "res\\softdeepclean_bg_duba.png")
DEFINE_PNG(IDP_1040,            1040,   "res\\softdeepclean_clean_duba.png")

DEFINE_PNG(IDP_1041,            1041,   "res\\drivermanager_bg.png")
DEFINE_PNG(IDP_1042,            1042,   "res\\drivermanager_setting_warn.png")
DEFINE_PNG(IDP_1043,            1043,   "res\\btn_green_big.png")
DEFINE_PNG(IDP_1044,            1044,   "res\\setting_img3.png")
DEFINE_PNG(IDP_1045,            1045,   "res\\btn_more.png")
DEFINE_PNG(IDP_1046,            1046,   "res\\new_btn_close.png")
DEFINE_PNG(IDP_1047,            1047,   "res\\icons.png")
DEFINE_PNG(IDP_1048,            1048,   "res\\biometricdevice.png")
DEFINE_PNG(IDP_1049,            1049,   "res\\bluetooth.png")
DEFINE_PNG(IDP_1050,            1050,   "res\\display.png")
DEFINE_PNG(IDP_1051,            1051,   "res\\media.png")
DEFINE_PNG(IDP_1052,            1052,   "res\\mouse.png")
DEFINE_PNG(IDP_1053,            1053,   "res\\net.png")
DEFINE_PNG(IDP_1054,            1054,   "res\\other.png")
DEFINE_PNG(IDP_1055,            1055,   "res\\printer.png")
DEFINE_PNG(IDP_1056,            1056,   "res\\scsiadapter.png")
DEFINE_PNG(IDP_1057,            1057,   "res\\smartcardreader.png")
DEFINE_PNG(IDP_1058,            1058,   "res\\system.png")
DEFINE_PNG(IDP_1059,            1059,   "res\\usb.png")
DEFINE_PNG(IDP_1060,            1060,   "res\\software.png")

DEFINE_PNG(IDP_1061,            1061,   "res\\filedestroy_bg.png")
DEFINE_PNG(IDP_1062,            1062,   "res\\btn_destroy.png")
DEFINE_PNG(IDP_1063,            1063,   "res\\drivermanager_bg_dg.png")
DEFINE_PNG(IDP_1064,            1064,   "res\\img_vip_flag.png")
DEFINE_PNG(IDP_1065,            1065,   "res\\drivermanager_bg_orange.png")
DEFINE_PNG(IDP_1066,            1066,   "res\\drivermanager_bg_blue.png")
DEFINE_PNG(IDP_1067,            1067,   "res\\logo_duba_white.png")
DEFINE_PNG(IDP_1068,            1068,   "res\\logo_dg_white.png")
DEFINE_PNG(IDP_1069,            1069,   "res\\btn_clean.png")
DEFINE_PNG(IDP_1070,            1070,   "res\\btn_cancel.png")
DEFINE_PNG(IDP_1071,            1071,   "res\\btn_setting.png")
DEFINE_PNG(IDP_1073,            1073,   "res\\btn_ok_2.png")
DEFINE_PNG(IDP_1074,            1074,   "res\\setting_img4.png")
DEFINE_PNG(IDP_1075,            1075,   "res\\img_explorer_logo.png")
DEFINE_PNG(IDP_1076,            1076,   "res\\btn_experience.png")
DEFINE_PNG(IDP_1077,            1077,   "res\\btn_explorer_close.png")
DEFINE_PNG(IDP_1079,            1079,   "res\\img_explorer_label.png")
DEFINE_PNG(IDP_1080,            1080,   "res\\bg_drivergenius.png")
DEFINE_PNG(IDP_1081,            1081,   "res\\img_bg2_drivergenius.png")
DEFINE_PNG(IDP_1082,            1082,   "res\\screencapture_taskbar_bg.png")
DEFINE_PNG(IDP_1083,            1083,   "res\\assoftmgr_pop_bg.png")
//System Slim Clean Resources
DEFINE_PNG(IDP_1084,            1084,   "res\\qqtrashbg.png")
DEFINE_PNG(IDP_1085,            1085,   "res\\img_vip_flag.png")
DEFINE_PNG(IDP_1086,            1086,   "res\\syslimlogo.png")
DEFINE_PNG(IDP_1087,            1087,   "res\\kcleanbtn.png")
DEFINE_PNG(IDP_1088,            1088,   "res\\btn_close_slim2.png")
DEFINE_PNG(IDP_1089,            1089,   "res\\btn_close_slim.png")
DEFINE_PNG(IDP_1090,            1090,   "res\\btn_set_slim.png")
DEFINE_PNG(IDP_1091,            1091,   "res\\kcleanbtn2.png")
DEFINE_PNG(IDP_1092,            1092,   "res\\btn_cancel_clean.png")
DEFINE_PNG(IDP_1093,            1093,   "res\\ksysslim_pop_dlg1.png")
DEFINE_PNG(IDP_1094,            1094,   "res\\startup_pop_bg1.png")
DEFINE_PNG(IDP_1095,            1095,   "res\\btn_setting_slim2.png")

//driver genius machine limit resources

DEFINE_PNG(IDP_1096,            1096,   "res\\disk.png")
DEFINE_PNG(IDP_1097,            1097,   "res\\btn_defrag_dark.png")
DEFINE_PNG(IDP_1098,            1098,   "res\\btn_defrag_light.png")
DEFINE_PNG(IDP_1099,            1099,   "res\\vip_flag.png")
DEFINE_PNG(IDP_1100,            1100,   "res\\drop_down_light.png")
DEFINE_PNG(IDP_1101,            1101,   "res\\close_light.png")
DEFINE_PNG(IDP_1102,            1102,   "res\\drop_down_dark.png")
DEFINE_PNG(IDP_1103,            1103,   "res\\close_dark.png")
DEFINE_PNG(IDP_1104,            1104,   "res\\logo_duba_blue.png")
DEFINE_PNG(IDP_1105,            1105,   "res\\sysslim.png")
DEFINE_PNG(IDP_1106,            1106,   "res\\btn_free_dark.png")
DEFINE_PNG(IDP_1107,            1107,   "res\\btn_free_light.png")
DEFINE_PNG(IDP_1108,            1108,   "res\\btn_uninstall_dark.png")
DEFINE_PNG(IDP_1109,            1109,   "res\\ico_uninstall.png")
DEFINE_PNG(IDP_1110,            1110,   "res\\btn_close_white.png")
DEFINE_PNG(IDP_1111,            1111,   "res\\antireinstall_tip_icon.png")
DEFINE_PNG(IDP_1112,            1112,   "res\\btn_skin_msg_box_btn_close_middle_white.png")
DEFINE_PNG(IDP_1113,            1113,   "res\\ico_warnning.png")
DEFINE_PNG(IDP_1114,            1114,   "res\\antireinstall_not_warnning.png")
DEFINE_PNG(IDP_1115,            1115,   "res\\antireinstall_setting.png")
DEFINE_PNG(IDP_1116,            1116,   "res\\setting_anti_reinstall.png")
DEFINE_PNG(IDP_1117,            1117,   "res\\anti_install_list_btn.png")
DEFINE_PNG(IDP_1118,            1118,   "res\\anti_reinstall_confirm_btn.png")
DEFINE_PNG(IDP_1119,            1119,   "res\\anti_reinstall_reminder.png")
DEFINE_PNG(IDP_1120,			1120,	"res\\anti_reinstall_btn.png")

DEFINE_PNG(IDP_1121,			1121,	"res\\KTemperature_ICON.png")
DEFINE_PNG(IDP_1122,			1122,	"res\\btn_temperature_dark.png")
DEFINE_PNG(IDP_1123,			1123,	"res\\btn_temperature_white.png")
DEFINE_PNG(IDP_1124,			1124,	"res\\KTemperatureHigh_ICON.png")
DEFINE_PNG(IDP_1125,			1125,	"res\\btn_poweroff_dark.png")
DEFINE_PNG(IDP_1126,			1126,	"res\\btn_poweroff_white.png")
DEFINE_PNG(IDP_1127,			1127,	"res\\btn_orange_type_3.png")
DEFINE_PNG(IDP_1128,			1128,	"res\\btn_set_type_5.png")
DEFINE_PNG(IDP_1129,			1129,	"res\\btn_close_type_5.png")
//DEFINE_PNG(IDP_1140,			1140,	"res\\internet_protect_img.png")

DEFINE_PNG(IDP_1141,			1141,	"res\\kdisk_mgr.png")
DEFINE_PNG(IDP_1142,			1142,	"res\\btn_diskmgr_dark.png")
DEFINE_PNG(IDP_1143,			1143,	"res\\kdisk_mgr_repeat.png")
DEFINE_PNG(IDP_1144,			1144,	"res\\kdisk_mgr_wechat.png")
DEFINE_PNG(IDP_1145,			1145,	"res\\kdisk_mgr_qq.png")

DEFINE_PNG(IDP_1146,			1146,	"res\\bluescreen\\bsod.png")
DEFINE_PNG(IDP_1147,			1147,	"res\\bluescreen\\bsod_btn_close.png")
DEFINE_PNG(IDP_1148,			1148,	"res\\bluescreen\\bsod_btn_fix.png")
DEFINE_PNG(IDP_1149,			1149,	"res\\bluescreen\\bsod_icon.png")
DEFINE_PNG(IDP_1150,			1150,	"res\\bluescreen\\bsod_title.png")

DEFINE_PNG(IDP_1151,			1151,	"res\\diskslow\\diskslow_light_icon.png")
DEFINE_PNG(IDP_1152,			1152,	"res\\diskslow\\diskslow_vip.png")
DEFINE_PNG(IDP_1153,			1153,	"res\\diskslow\\diskslow_snail.png")
DEFINE_PNG(IDP_1154,			1154,	"res\\diskslow\\diskslow_light_btn_ok.png")
DEFINE_PNG(IDP_1155,			1155,	"res\\diskslow\\diskslow_dark_btn_ok.png")
DEFINE_PNG(IDP_1156,			1156,	"res\\diskslow\\diskslow_dark_menu.png")
DEFINE_PNG(IDP_1157,			1157,	"res\\diskslow\\diskslow_dark_close.png")
DEFINE_PNG(IDP_1158,			1158,	"res\\diskslow\\diskslow_light_menu.png")
DEFINE_PNG(IDP_1159,			1159,	"res\\diskslow\\diskslow_light_close.png")
DEFINE_PNG(IDP_1160,			1160,	"res\\diskslow\\diskslow_dark_icon.png")

DEFINE_PNG(IDP_1161,			1161,	"res\\satisfaction\\satisfaction_logo.png")
DEFINE_PNG(IDP_1162,			1162,	"res\\satisfaction\\satisfaction_menu.png")
DEFINE_PNG(IDP_1163,			1163,	"res\\satisfaction\\satisfaction_close.png")
//DEFINE_PNG(IDP_1164,			1164,	"res\\satisfaction\\satisfaction_na.png")
//DEFINE_PNG(IDP_1165,			1165,	"res\\satisfaction\\satisfaction_ok.png")
DEFINE_PNG(IDP_1166,			1166,	"res\\satisfaction\\satisfaction_commit.png")
//DEFINE_PNG(IDP_1167,			1167,	"res\\satisfaction\\satisfaction_full_edit.png")
//DEFINE_PNG(IDP_1168,			1168,	"res\\satisfaction\\satisfaction_na_click.png")
//DEFINE_PNG(IDP_1169,			1169,	"res\\satisfaction\\satisfaction_ok_click.png")
DEFINE_PNG(IDP_1170,			1170,	"res\\satisfaction\\satisfaction_contact_border.png")
DEFINE_PNG(IDP_1171,			1171,	"res\\satisfaction\\satisfaction_contact_split.png")
//DEFINE_PNG(IDP_1172,			1172,	"res\\satisfaction\\satisfaction_label.png")
DEFINE_PNG(IDP_1173,			1173,	"res\\satisfaction\\satisfaction_edit.png")
DEFINE_PNG(IDP_1174,			1174,	"res\\satisfaction\\satisfaction_label_selected.png")
DEFINE_PNG(IDP_1175,			1175,	"res\\satisfaction\\satisfaction_arrow_down.png")
DEFINE_PNG(IDP_1176,			1176,	"res\\satisfaction\\satisfaction_arrow_up.png")
DEFINE_PNG(IDP_1177,			1177,	"res\\satisfaction\\satisfaction_droplist_bg.png")
DEFINE_PNG(IDP_1178,			1178,	"res\\satisfaction\\satisfaction_person.png")
DEFINE_PNG(IDP_1179,			1179,	"res\\satisfaction\\satisfaction_qq.png")
DEFINE_PNG(IDP_1180,			1180,	"res\\satisfaction\\satisfaction_crm.png")
DEFINE_PNG(IDP_1181,			1181,	"res\\satisfaction\\satisfaction_label_normal.png")
DEFINE_PNG(IDP_1182,			1182,	"res\\satisfaction\\satisfaction_label_hover.png")
DEFINE_PNG(IDP_1183,			1183,	"res\\satisfaction\\satisfaction_contact_border_highlight.png")

DEFINE_PNG(IDP_1184,			1184,	"res\\satisfaction\\satisfaction_fine_normal.png")
DEFINE_PNG(IDP_1185,			1185,	"res\\satisfaction\\satisfaction_fine_hover.png")
DEFINE_PNG(IDP_1186,			1186,	"res\\satisfaction\\satisfaction_fine_clicked.png")

DEFINE_PNG(IDP_1187,			1187,	"res\\satisfaction\\satisfaction_un_normal.png")
DEFINE_PNG(IDP_1188,			1188,	"res\\satisfaction\\satisfaction_un_hover.png")
DEFINE_PNG(IDP_1189,			1189,	"res\\satisfaction\\satisfaction_un_clicked.png")

DEFINE_PNG(IDP_1190,			1190,	"res\\dumprightmenu\\dump_rightmenu_middle_bg.png")
DEFINE_PNG(IDP_1191,			1191,	"res\\dumprightmenu\\dump_rightmenu_middle_btn.png")
DEFINE_PNG(IDP_1192,			1192,	"res\\dumprightmenu\\dump_rightmenu_middle_computer.png")
DEFINE_PNG(IDP_1193,			1193,	"res\\dumprightmenu\\dump_rightmenu_middle_exclamation.png")
DEFINE_PNG(IDP_1194,			1194,	"res\\dumprightmenu\\dump_rightmenu_middle_soft.png")
DEFINE_PNG(IDP_1195,			1195,	"res\\dumprightmenu\\dump_rightmenu_right_btn.png")
DEFINE_PNG(IDP_1196,			1196,	"res\\dumprightmenu\\dump_rightmenu_right_computer.png")
DEFINE_PNG(IDP_1197,			1197,	"res\\dumprightmenu\\dump_rightmenu_right_exclamation.png")
DEFINE_PNG(IDP_1198,			1198,	"res\\dumprightmenu\\dump_rightmenu_right_soft.png")
DEFINE_PNG(IDP_1199,			1199,	"res\\dumprightmenu\\dump_rightmenu_close.png")
DEFINE_PNG(IDP_1200,			1200,	"res\\dumprightmenu\\dump_rightmenu_menu.png")
DEFINE_PNG(IDP_1201,			1201,	"res\\dumprightmenu\\dump_rightmenu_right_bg.png")

//DEFINE_PNG(IDP_1202,			1202,	"res\\usb\\usb_icon_a.png")
//DEFINE_PNG(IDP_1203,			1203,	"res\\usb\\usb_icon_b.png")
DEFINE_PNG(IDP_1204,			1204,	"res\\setting_dark_12px.png")
//DEFINE_PNG(IDP_1205,			1205,	"res\\usb\\usb_btn_ok_dark.png")
//DEFINE_PNG(IDP_1206,			1206,	"res\\usb\\usb_btn_ok_green.png")


DEFINE_PNG(IDP_1207,			1207,	"res\\semauthority\\semauthority_bg.png")
DEFINE_PNG(IDP_1208,			1208,	"res\\semauthority\\semauthority_logo.png")
DEFINE_PNG(IDP_1209,			1209,	"res\\semauthority\\semauthority_step.png")
DEFINE_PNG(IDP_1210,			1210,	"res\\semauthority\\semauthority_boot.png")
DEFINE_PNG(IDP_1211,			1211,	"res\\semauthority\\semauthority_popwidget.png")
DEFINE_PNG(IDP_1212,			1212,	"res\\semauthority\\semauthority_rightmenu.png")
DEFINE_PNG(IDP_1213,			1213,	"res\\semauthority\\semauthority_fileassociation.png")
DEFINE_PNG(IDP_1214,			1214,	"res\\semauthority\\semauthority_switch_on.png")
DEFINE_PNG(IDP_1215,			1215,	"res\\semauthority\\semauthority_switch_off.png")
DEFINE_PNG(IDP_1216,			1216,	"res\\semauthority\\semauthority_click.png")
DEFINE_PNG(IDP_1217,			1217,	"res\\semauthority\\semauthority_cleaner_inst.png")
DEFINE_PNG(IDP_1218,			1218,	"res\\semauthority\\semauthority_cleaner_network.png")
DEFINE_PNG(IDP_1219,			1219,	"res\\semauthority\\semauthority_cleaner_system.png")
DEFINE_PNG(IDP_1220,			1220,	"res\\semauthority\\semauthority_cleaner_soft.png")
DEFINE_PNG(IDP_1221,			1221,	"res\\semauthority\\semauthority_cleaner_click.png")
DEFINE_PNG(IDP_1222,			1222,	"res\\semauthority\\semauthority_default_icon.png")

//DEFINE_PNG(IDP_1223,			1223,	"res\\diskslowv2\\bg.png")
//DEFINE_PNG(IDP_1224,			1224,	"res\\diskslowv2\\btn.png")
//DEFINE_PNG(IDP_1225,			1225,	"res\\diskslowv2\\close.png")
//DEFINE_PNG(IDP_1226,			1226,	"res\\diskslowv2\\nomoretip.png")
//DEFINE_PNG(IDP_1227,			1227,	"res\\diskslowv2\\defaultsoft.png")

DEFINE_PNG(IDP_1228,			1228,	"res\\systemtimexception\\icon_systemtime_exception.png")
DEFINE_PNG(IDP_1229,			1229,	"res\\systemtimexception\\systemtime_btn_ok_green.png")
DEFINE_PNG(IDP_1230,			1230,	"res\\internetprotectplugin\\icon_internet_protect_chrome.png")
DEFINE_PNG(IDP_1231,			1231,	"res\\internetprotectplugin\\icon_internet_protect_edge.png")

DEFINE_PNG(IDP_1232,			1232,	"res\\audioabnormal\\icon_audio_abnormal_service.png")
DEFINE_PNG(IDP_1233,			1233,	"res\\printerabnormal\\icon_printer_abnormal_service.png")

DEFINE_PNG(IDP_1234,			1234,	"res\\searchbox\\btn_close.png")
DEFINE_PNG(IDP_1235,			1235,	"res\\searchbox\\btn_drop.png")
DEFINE_PNG(IDP_1236,			1236,	"res\\searchbox\\bg.png")
DEFINE_PNG(IDP_1237,			1237,	"res\\searchbox\\btn_ok.png")

DEFINE_PNG(IDP_1238,			1238,	"res\\winoptimize\\btn_menu.png")
DEFINE_PNG(IDP_1239,			1239,	"res\\winoptimize\\img_icon.png")
DEFINE_PNG(IDP_1240,			1240,	"res\\winoptimizetaskbar\\btn_optimize.png")
DEFINE_PNG(IDP_1241,			1241,	"res\\winoptimizetaskbar\\img_animate_bk.png")
DEFINE_PNG(IDP_1242,			1242,	"res\\winoptimizetaskbar\\img_animate_search.png")
DEFINE_PNG(IDP_1243,			1243,	"res\\winoptimizetaskbar\\img_animate_circle.png")
DEFINE_PNG(IDP_1244,			1244,	"res\\winoptimizetaskbar\\img_animate_rectangle.png")
DEFINE_PNG(IDP_1245,			1245,	"res\\winoptimizetaskbar\\img_animate_weather.png")
DEFINE_PNG(IDP_1246,			1246,	"res\\winoptimizetaskbar\\img_finish.png")
DEFINE_PNG(IDP_1247,			1247,	"res\\close_btn_red_round_24x24.png")
DEFINE_PNG(IDP_1248,			1248,	"res\\menu_btn_gray_24x24.png")
DEFINE_PNG(IDP_1249,			1249,	"res\\logo_duba_light_blue_16x16.png")

DEFINE_PNG(IDP_1250,			1250,	"res\\bluescreen\\bsod_icon_dg.png")
DEFINE_PNG(IDP_1251,			1251,	"res\\internetexception\\icon_internet_exception.png")
DEFINE_PNG(IDP_1252,			1252,	"res\\icon_dg_driver_update.png")

DEFINE_PNG(IDP_1253,			1253,	"res\\trashclean\\dg_recycle_icon.png")
DEFINE_PNG(IDP_1254,			1254,	"res\\trashclean\\dg_recycle_pop_btn_clean.png")
DEFINE_PNG(IDP_1255,			1255,	"res\\trashclean\\dg_recycle_pop_btn_close.png")
DEFINE_PNG(IDP_1256,			1256,	"res\\trashclean\\dg_recycle_pop_pregress_flight.png")
DEFINE_PNG(IDP_1257,			1257,	"res\\trashclean\\dg_recycle_pop_scanning_bg.png")
DEFINE_PNG(IDP_1258,			1258,	"res\\trashclean\\dg_recycle_pop_scanover_bg.png")
DEFINE_PNG(IDP_1259,			1259,	"res\\trashclean\\dg_recycle_pop_text_scaning.png")
DEFINE_PNG(IDP_1260,			1260,	"res\\trashclean\\dg_recycle_pop_text_scaning2.png")
DEFINE_PNG(IDP_1261,			1261,	"res\\trashclean\\dg_recylce_pop_menu_btn.png")



