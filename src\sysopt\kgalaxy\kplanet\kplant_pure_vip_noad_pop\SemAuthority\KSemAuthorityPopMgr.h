#pragma once

#include "cf_public/core/json/BkJson.h"
#include "KSafeApiHelper.h"

class KSemAuthorityPopMgr
{
public:
	
	struct RightMenuCfg
	{
		CString strRegPath;
		CString strRegKey;
        CString strRegShellValue;
        CString strRegShellExValue;
	};

    struct FileAssociationCfg
    {
        CString strExt;
        CString strProgID;
    };

    struct SoftCfg
    {
        DWORD dwSoftParentID;
        BOOL  bBootSwitch;
        BOOL  bPopWidgetSwitch;

        CString strSoftName;

        std::vector<RightMenuCfg> vecRightMenu;
        std::vector<FileAssociationCfg> vecFileAssociation;

        SoftCfg() : dwSoftParentID(0), bBootSwitch(FALSE), bPopWidgetSwitch(FALSE){}
    };
	
    KSemAuthorityPopMgr();
    ~KSemAuthorityPopMgr();

    BOOL LoadConfig(DWORD dwSoftParentID);

    BOOL HasBoot();
    BOOL HasPopWidget();
    BOOL HasRightMenu();
    BOOL HasFileAssociation();

    void CleanRightMenu();
    void CleanFileAssociation();

protected:
    void ParseRightMenuCfg(cf::BkJson::Handle& jsonRightMenu);
    void ParseFileAssociationCfg(cf::BkJson::Handle& jsonFileAssociation);

protected:
    CString GetUserChoiceProgID(const CString& strExt);
    CString GetClassRootProgID(const CString& strExt);
    void SetUserChoiceProgID(LPCTSTR lpszExt, LPCTSTR lpsProgID);
    void SetClassRootProgID(LPCTSTR lpszExt, LPCTSTR lpsProgID);
    void SetUserChoiceProgIDSaftapi(LPCTSTR lpszExt, LPCTSTR lpsProgID);

    BOOL CheckRegShellValue(const RightMenuCfg& rightMenu);
    BOOL CheckRegShellExValue(const RightMenuCfg& rightMenu);
    void DeleteRegShellKey(const RightMenuCfg& rightMenu);
    void DeleteRegShellExKey(const RightMenuCfg& rightMenu);
    BOOL SafeRecurseDeleteKey(HKEY hKey,LPCTSTR rightMenu);
protected:
    int     m_nVersion;
    SoftCfg m_softItemCfg;

    ISafeApiHelper* m_IPSafeApiHelper;
};
